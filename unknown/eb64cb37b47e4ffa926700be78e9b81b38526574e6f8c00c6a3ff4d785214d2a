import json
from datetime import <PERSON><PERSON><PERSON>
from typing import List, Optional, Type, Literal
from uuid import UUID

import requests
import structlog
import jwt
from django.conf import settings
from django.core.exceptions import PermissionDenied
from django.db.models import QuerySet
from django.http import (
    HttpResponse,
    StreamingHttpResponse,
)
from django.shortcuts import get_object_or_404
from ninja.errors import ValidationError, HttpError
from pydantic import BaseModel, ValidationError as PydanticValidationError
from requests import Request

from bekb.bekbfipla_factory import BekbFiplaAccountFactory
from bekb.fakes import BekbAccountFactoryFaker
from bekb.models import (
    Partner,
    BEKBDossierProperties,
    Attribute,
    BEKBDossierExport,
    ExportFeedback,
)
from bekb.schemas.schemas import (
    Parkey,
    AccountNameFipla,
    AccountNameMortgage,
)

from bekb.services import (
    update_or_create_user,
    update_fico,
    create_bekb_dossier,
    map_user_to_schema,
    set_pers,
)
from dossier.models import Account, DossierUser, UserInvolvement, Dossier
from bekb.schemas import schemas
from dossier.services import change_dossier_work_status
from statemgmt.models import Status

from typing import Union, Tuple
from django.utils import timezone

logger = structlog.get_logger()


def decode_jwt_or_render_error(
    encoded_jwt: str, error_redirect_url: str
) -> dict | HttpResponse:
    """
    Decodes the given JWT token. If decoding fails, returns an HttpResponse
    with an error page. Otherwise, returns the decoded dictionary.
    """
    try:
        return jwt.decode(
            encoded_jwt, settings.BEKB_SHARED_SECRET, algorithms=["HS256"]
        )
    except jwt.exceptions.PyJWTError:
        logger.exception("Could not decode encoded_jwt")
        error_redirect_url = error_redirect_url
        en_head = "Link not valid anymore"
        en_body = (
            f"Please select the relevant partner / multi-partner in DBP and open HypoDossier again "
            f"or go to the HypoDossier <a href={error_redirect_url}>list of dossiers</a>."
        )
        de_head = "Link nicht mehr gültig."
        de_body = (
            f"Bitte in DBP im entsprechenden Partner/Multipartner erneut HypoDossier aufrufen oder zur "
            f"allgemeinen HypoDossier <a href={error_redirect_url}>Dossier-Übersicht</a> wechseln."
        )
        return HttpResponse(
            f"<html>"
            f"<body>"
            f'<div style="display: flex; justify-content: center; align-items: center; flex-direction: column;">'
            f'<h1 style="font-weight: normal;">{de_head}</h1>'
            f'<h3 style="font-weight: normal;">{de_body}</h3>'
            "<br/><br/>"
            f'<h1 style="font-weight: normal;">{en_head}</h1>'
            f'<h3 style="font-weight: normal;">{en_body}</h3>'
            f"</div>"
            f"</body>"
            f"</html>"
        )


def fix_account_name(decoded_jwt: dict, old_name: str, new_name: str) -> None:
    """
    Mutates the `decoded_jwt` dictionary, replacing decoded_jwt["account_name"]
    from old_name to new_name if it matches.
    """
    if decoded_jwt.get("account_name") == old_name:
        decoded_jwt["account_name"] = new_name


def process_common_dossier_logic(
    decoded_jwt: dict, schema_cls, use_fico: bool = False
) -> str:
    """
    1. Parse decoded_jwt with a given Pydantic schema (e.g., DossierShowJWTFipla or DossierShowJWT).
    2. Get or create relevant models (Account, Partner, etc.).
    3. Update or create any user references.
    4. Update existing dossiers to link correct Partners / FICO user (if needed).
    5. Return the final redirect URL.

    :param decoded_jwt: The already-decoded JWT payload.
    :param schema_cls:  Which schema class (Pydantic) to use for parsing.
    :param use_fico:   Whether we expect a `fico` user in the JWT that needs updating.
    :return:            The final DMF endpoint URL (string) to which the view can redirect.
    """
    dossier_show = schema_cls(**decoded_jwt)

    # Fetch the account
    account = Account.objects.get(key=dossier_show.account_name)

    # Update/create user(s)
    update_or_create_user(dossier_show.current_user, account)
    if use_fico and hasattr(dossier_show, "fico"):
        update_or_create_user(dossier_show.fico, account)

    # Update/create the main business partner
    business_partner, _ = Partner.objects.update_or_create(
        dossier_show.business_partner.model_dump(),
        parkey=dossier_show.business_partner.parkey,
        account=account,
    )

    # Optionally handle partner_partner
    partner_partner = None
    if (
        hasattr(dossier_show, "partner_partner")
        and dossier_show.partner_partner is not None
    ):
        partner_partner, _ = Partner.objects.update_or_create(
            dossier_show.partner_partner.model_dump(),
            parkey=dossier_show.partner_partner.parkey,
            account=account,
        )

    # Update existing dossiers
    dossiers = BEKBDossierProperties.objects.filter(
        business_partner__parkey=dossier_show.business_partner.parkey
    ).all()

    for bekb_dossier in dossiers:
        bekb_dossier.business_partner = business_partner

        # For the second schema, there's a partner_partner & fico
        if partner_partner:
            bekb_dossier.partner_partner = partner_partner

        if use_fico and hasattr(dossier_show, "fico"):
            update_fico(account, bekb_dossier.dossier, dossier_show.fico)

        bekb_dossier.save()

    if hasattr(dossier_show, "dossier_uuid") and dossier_show.dossier_uuid:
        # If we are given a dossier uuid, redirect directly to it
        return f"{account.dmf_endpoint}/dossier/{dossier_show.dossier_uuid}/view/page?lang={dossier_show.language.value}"
    else:
        return f"{account.dmf_endpoint}/dashboard?business_parkey={dossier_show.business_partner.parkey}&lang={dossier_show.language.value}"


def get_dossiers_for_business_partner(
    account: Account, business_parkey: Parkey
) -> List[schemas.DossierShow]:
    """
    Retrieve and transform dossier information for a business partner.

    Args:
        account: The account to query dossiers for
        business_parkey: The business partner's parkey

    Returns:
        List of DossierShow schemas containing dossier information
    """
    dossier_properties = (
        BEKBDossierProperties.objects.filter(
            account=account,
            business_partner__parkey=business_parkey,
            dossier__expiry_date__gt=timezone.now(),
        )
        .select_related("dossier__work_status", "business_partner")
        .order_by("-dossier__created_at")
        .all()
    )

    ds_schemas = []

    for dp in dossier_properties:
        ds_schema = schemas.DossierShow(
            dossier_uuid=dp.dossier.uuid,
            business_partner=schemas.BusinessPartner(
                parkey=business_parkey,
                name=dp.business_partner.name,
                firstname=dp.business_partner.firstname,
                pers=dp.business_partner.pers,
            ),
            dossier_name=dp.dossier.name,
            language=dp.dossier.lang,
            access_mode=dp.dossier.access_mode,
            work_status_key=dp.dossier.work_status.key,
            work_status_name_de=dp.dossier.work_status.name_de,
            work_status_name_en=dp.dossier.work_status.name_en,
            work_status_name_fr=dp.dossier.work_status.name_fr,
            work_status_name_it=dp.dossier.work_status.name_it,
            created_at=dp.dossier.created_at,
            updated_at=dp.dossier.updated_at,
            expires_at=dp.dossier.expiry_date,
        )

        if dp.dossier.businesscase_type:
            ds_schema.businesscase_type_key = dp.dossier.businesscase_type.key
            ds_schema.businesscase_type_name_de = dp.dossier.businesscase_type.name_de
            ds_schema.businesscase_type_name_en = dp.dossier.businesscase_type.name_en
            ds_schema.businesscase_type_name_fr = dp.dossier.businesscase_type.name_fr
            ds_schema.businesscase_type_name_it = dp.dossier.businesscase_type.name_it

        involvement_assignee = UserInvolvement.objects.filter(
            dossier=dp.dossier, role__key="ASSIGNEE"
        ).first()
        if involvement_assignee:
            ds_schema.assignee_username = involvement_assignee.user.user.username
            ds_schema.assignee_firstname = involvement_assignee.user.user.first_name
            ds_schema.assignee_lastname = involvement_assignee.user.user.last_name

        involvement_fico = UserInvolvement.objects.filter(
            dossier=dp.dossier, role__key="FICO"
        ).first()
        if involvement_fico:
            ds_schema.fico_username = involvement_fico.user.user.username
            ds_schema.fico_firstname = involvement_fico.user.user.first_name
            ds_schema.fico_lastname = involvement_fico.user.user.last_name

        ds_schemas.append(ds_schema)
    return ds_schemas


def get_error_response() -> HttpResponse:
    """Generate bilingual error response HTML."""
    en_head = "Link not valid anymore"
    en_body = "Please create a new dossier for HypoDossier in DBP (in the relevant Partner / Multipartner"
    de_head = "Link nicht mehr gültig"
    de_body = "Bitte in DBP im entsprechenden Partner/Multipartner ein neues Dossier für HypoDossier erstellen"

    return HttpResponse(
        f"<html><body>"
        f'<div style="display: flex; justify-content: center; align-items: center; flex-direction: column;">'
        f'<h1 style="font-weight: normal;">{de_head}</h1>'
        f'<h3 style="font-weight: normal;">{de_body}</h3>'
        "</br></br>"
        f'<h1 style="font-weight: normal;">{en_head}</h1>'
        f'<h3 style="font-weight: normal;">{en_body}</h3>'
        f"</div></body></html>"
    )


def process_dossier_jwt(encoded_jwt: str) -> Union[dict, HttpResponse]:
    """Process and validate the JWT token."""
    try:
        decoded_jwt = jwt.decode(
            encoded_jwt, settings.BEKB_SHARED_SECRET, algorithms=["HS256"]
        )

        if decoded_jwt["account_name"] == "bekb":
            decoded_jwt["account_name"] = "bekbp"

        if "dossier_name" not in decoded_jwt:
            decoded_jwt["dossier_name"] = (
                f"Dossier vom {timezone.localtime(timezone.now())}"
            )

        return decoded_jwt
    except jwt.exceptions.PyJWTError:
        logger.exception("Could not decode encoded_jwt")
        return get_error_response()


def create_dossier_from_jwt(
    encoded_jwt: str,
    schema_class,
    account_type: Literal["bekb", "bekbfipla"],
    use_fico: bool = True,
) -> Tuple[Account, str]:
    """Create a dossier from JWT data.

    Args:
        encoded_jwt: The JWT token to decode
        schema_class: Schema class for validation
        use_fico: Whether to use FICO
        account_type: Type of account to validate against ("bekb" or "bekbfipla")

    Raises:
        ValueError: If account name doesn't match the specified account type
    """

    decoded_jwt = process_dossier_jwt(encoded_jwt)

    if isinstance(decoded_jwt, HttpResponse):
        return decoded_jwt

    validate_jwt_schema_with_http_error(schema_class, decoded_jwt)

    dossier_create = schema_class(**decoded_jwt)
    account_name = dossier_create.account_name

    # Validate account name matches the expected type
    if account_type == "bekb":
        valid_accounts = {account.value for account in AccountNameMortgage}
        if account_name not in valid_accounts:
            raise ValidationError(
                f"Account name '{account_name}' is not a valid BEKB account"
            )
    else:  # bekbfipla
        valid_accounts = {account.value for account in AccountNameFipla}
        if account_name not in valid_accounts:
            raise ValidationError(
                f"Account name '{account_name}' is not a valid BEKBFIPLA account"
            )

    account = Account.objects.get(key=account_name)

    new_bekb_dossier = create_bekb_dossier(account, dossier_create, use_fico=use_fico)

    response_url = (
        f"{account.dmf_endpoint}/dossier/{new_bekb_dossier.dossier.uuid}"
        f"/view/page?lang={dossier_create.language.value}"
    )

    return account, response_url


def get_hypodossier_users(
    account_name: Union[AccountNameFipla, AccountNameMortgage],
) -> List[schemas.User]:
    """Retrieves all HypoDossier users for a specific account.

    Args:
        account_name (AccountName): The name of the account.

    Returns:
        List[schemas.User]: The list of HypoDossier users for the specified account.
    """
    dossier_users: QuerySet[DossierUser] = (
        DossierUser.objects.filter(account__key=account_name)
        .exclude(user__email__endswith="@hypodossier.ch")
        .prefetch_related("account", "user__groups")
        .all()
    )

    return [map_user_to_schema(dossier_user) for dossier_user in dossier_users]


def update_user_attributes_service(
    account_name: Union[AccountNameFipla, AccountNameMortgage],
    updates: List[schemas.UserAttributes],
) -> None:
    """Service function to update user attributes.

    Args:
        account_name (AccountName): The name of the account where the user belongs.
        updates (List[schemas.UserAttributes]): The list of user attributes to update.

    Raises:
        Account.DoesNotExist: If the account is not found.
        DossierUser.DoesNotExist: If the user is not found in the account.
    """
    account = Account.objects.get(key=account_name)
    for update in updates:
        dossier_user = DossierUser.objects.prefetch_related("user").get(
            user__username=update.username, account=account
        )
        user = dossier_user.user
        if update.firstname:
            user.first_name = update.firstname

        if update.name:
            user.last_name = update.name

        if update.active is not None:
            user.is_active = update.active

        if update.pers is not None:
            set_pers(account, user, update.pers)

        user.save()


def process_business_partner_update(
    account: Account, update: schemas.BusinessPartnerUpdate
) -> Optional[str]:
    """
    Update the 'business_partner' fields in the database.

    Args:
        account (Account): The Account instance for which we are updating the partner.
        update (schemas.BusinessPartnerUpdate): The update payload for the business partner.

    Returns:
        str or None: A string representation of the old partner, or None if no update was made.
    """
    if update.business_partner is not None:
        business_partner = Partner.objects.get(
            account=account, parkey=update.business_parkey
        )
        old_business_partner = str(business_partner)
        business_partner.name = update.business_partner.name
        business_partner.firstname = update.business_partner.firstname

        if update.business_partner.pers is not None:
            business_partner.pers = update.business_partner.pers
            for dossier_properties in business_partner.business.all():
                dossier_properties.pers = update.business_partner.pers
                dossier_properties.save()

        business_partner.save()
        return old_business_partner

    return None


def process_partner_partner_update(
    account_name: str, account: Account, update: schemas.BusinessPartnerUpdate
) -> None:
    """
    Update the 'partner_partner' fields in the database.

    Args:
        account_name (str): The string key of the account.
        account (Account): The Account instance for which we are updating the partner_partner.
        update (schemas.BusinessPartnerUpdate): The update payload for the partner_partner.
    """
    if update.partner_partner is not None:
        properties = BEKBDossierProperties.objects.filter(
            dossier__account__key=account_name,
            business_partner__parkey=update.business_parkey,
            account=account,
        )
        partner_partner, _ = Partner.objects.update_or_create(
            update.partner_partner.model_dump(),
            parkey=update.partner_partner.parkey,
            account=account,
        )
        for dossier_properties in properties:
            dossier_properties.partner_partner = partner_partner
            dossier_properties.save()


def api_create_or_update_attribute(
    account_name: Union[AccountNameFipla, AccountNameMortgage],
    attribute_updates: schemas.AttributeUpdates,
):

    account = Account.objects.get(key=account_name)
    for update in attribute_updates.root:
        defaults = update.model_dump()
        del defaults["entity"]
        Attribute.objects.update_or_create(
            defaults, entity=update.entity, key=update.key, account=account
        )


def api_download_file(
    account_name: Union[AccountNameFipla, AccountNameMortgage],
    export_uuid: UUID,
) -> StreamingHttpResponse:
    """
    Downloads a Dossier associated with a given account and export UUID.

    Args:
        request: The incoming request object.
        account_name: The name of the account.
        export_uuid: The unique identifier of the export.

    Returns:
        StreamingHttpResponse: The file to be downloaded.
    """
    account = Account.objects.get(key=account_name)
    dossier_export = BEKBDossierExport.objects.get(account=account, uuid=export_uuid)

    url = dossier_export.file.fast_url
    r = requests.get(url, stream=True)

    response = StreamingHttpResponse(streaming_content=r)
    response["Content-Disposition"] = (
        f'attachment; filename="{dossier_export.file.name}"'
    )
    return response


def api_add_dossier_export_feedback(
    account_name: Union[AccountNameFipla, AccountNameMortgage],
    feedbacks: List[schemas.DossierExportFeedback],
):

    account = Account.objects.get(key=account_name)

    messages = []
    for feedback in feedbacks:
        dossier_export = BEKBDossierExport.objects.get(
            account=account, uuid=feedback.export_uuid
        )

        if feedback.status == schemas.ExportStatus.SUCCESS:
            status = ExportFeedback.Status.SUCCESS
            new_status = Status.objects.get(
                state_machine=account.active_work_status_state_machine,
                key="EXPORT_DONE",
            )
            dossier_export.dossier.expiry_date = timezone.now() + timedelta(days=2)
        else:
            status = ExportFeedback.Status.ERROR
            new_status = Status.objects.get(
                state_machine=account.active_work_status_state_machine,
                key="EXPORT_ERROR",
            )
        ExportFeedback.objects.create(
            account=account,
            export=dossier_export,
            status=status,
            message=feedback.message,
        )

        old_status = dossier_export.dossier.work_status

        if old_status == new_status:
            # We have received this status before, do not update status as this will throw a StateTransitionError
            msg = f"Skip export feedback {feedback.export_uuid} for dossier {dossier_export.dossier.uuid} because dossier already in status {old_status.key}."
            logger.warning(msg)
            messages.append(msg)
        else:
            change_dossier_work_status(
                dossier_export.dossier, dict(is_system=True, is_user=False), new_status
            )
            dossier_export.dossier.save()


def create_test_dossiers_ready_for_export(
    account_name: Union[AccountNameFipla, AccountNameMortgage],
    factory_class: Type[Union[BekbAccountFactoryFaker, BekbFiplaAccountFactory]],
    count: int = 1,
    **factory_kwargs,
) -> List[Dossier]:
    """
    Create test dossiers using the specified factory class.

    Args:
        account_name: The name of the account
        factory_class: The factory class to use for creating dossiers
        count: Number of dossiers to create (default: 1)
        **factory_kwargs: Additional keyword arguments for the factory

    Raises:
        PermissionDenied: If attempting to use production accounts
    """
    if account_name in [AccountNameMortgage.bekbp, AccountNameFipla.bekbfiplap]:
        raise PermissionDenied(f"Unauthorized for production account {account_name}")

    account = get_object_or_404(Account, key=account_name)
    account_factory = factory_class(account=account, **factory_kwargs)

    created_dossiers = []

    for _ in range(count):
        bekb_dossier: BEKBDossierProperties = (
            account_factory.create_ready_for_export_dossier(
                min_num_documents=1, allow_empty_docs=False
            )
        )

        created_dossiers.append(bekb_dossier.dossier)

    return created_dossiers


def validate_jwt_schema_with_http_error(
    schema: type[BaseModel], decoded_payload: dict
) -> None:
    """Validates a decoded JWT payload against a Pydantic schema.

    Args:
        schema: A Pydantic schema class to validate against
        decoded_payload: The decoded JWT payload to validate

    Raises:
        HttpError: With status 400 if validation fails, including error details

    Example:
        validate_jwt_schema(ReadJWTSchema, auth.decoded_payload)
    """
    try:
        # Validate JWT against provided schema
        schema.model_validate(decoded_payload)
    except PydanticValidationError as e:
        # Return a 400 Bad Request with validation details
        raise HttpError(
            400,
            json.dumps({"message": "Invalid JWT format", "details": str(e.errors())}),
        )


def validate_jwt_account_name_access(
    request: Request, account_key: str, raise_http_error: bool = True
) -> Account:
    """
    Validates that the authenticated user has access to the requested account.

    Args:
        request: The HTTP request object containing auth information
        account_key: The account key/name to validate access for
        raise_http_error: Whether to raise an HttpError or just log

    Raises:
        Http404: If account is not found
        HttpError: If user does not have permission to access the account
    """
    account = get_object_or_404(Account, key=account_key)
    if request.auth.account != account:
        if raise_http_error:
            raise HttpError(
                403,
                "account_name provided in the URL does not match the account in the JWT",
            )
        else:
            logger.warning(
                f"account_name provided in the URL {account.key} does not match the account in the JWT {request.auth.account.key}"
            )

    return account
