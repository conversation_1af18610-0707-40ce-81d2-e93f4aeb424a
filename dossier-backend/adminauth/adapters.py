# main_app/adapters.py
import jwt

from allauth.core.exceptions import ImmediateHttpResponse
from allauth.socialaccount.adapter import DefaultSocialAccountAdapter
from django.contrib import auth
from django.shortcuts import redirect

from django.contrib.auth import get_user_model

User = get_user_model()


class CustomAccountAdapter(DefaultSocialAccountAdapter):
    def is_open_for_signup(self, request, sociallogin):
        decoded_payload = jwt.decode(
            sociallogin.token.token,
            key="",
            audience="account",
            algorithms=["RS256"],
            options={"verify_signature": False, "exp": True},
        )

        roles = (
            decoded_payload.get("resource_access", {})
            .get("django-admin", {})
            .get("roles", {})
        )

        if "django-admin-role" in roles:
            return True

        if request.user.is_authenticated:
            auth.logout(request)

        raise ImmediateHttpResponse(redirect("account_logout"))
