from allauth.socialaccount.signals import (
    social_account_updated,
)

from django.dispatch import receiver
import jwt
from django.contrib.auth import get_user_model

User = get_user_model()


@receiver(social_account_updated)
def handle_social_account_updated(request, sociallogin, **kwargs):
    decoded_payload = jwt.decode(
        sociallogin.token.token,
        key="",
        audience="account",
        algorithms=["RS256"],
        options={"verify_signature": False, "exp": True},
    )

    user: User = sociallogin.account.user

    roles = (
        decoded_payload.get("resource_access", {})
        .get("django-admin", {})
        .get("roles", {})
    )

    if not user.is_staff or not user.is_superuser:
        if "django-admin-role" in roles:
            user.is_staff = True
            user.is_superuser = True
            user.save()
