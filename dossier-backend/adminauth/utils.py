from importlib import import_module


from allauth.socialaccount import providers


def get_provider_patterns():
    provider_urlpatterns = []
    provider_classes = providers.registry.get_class_list()

    for provider_class in [
        cls for cls in provider_classes if cls.id == "openid_connect"
    ]:
        prov_mod = import_module(provider_class.get_package() + ".urls")
        prov_urlpatterns = getattr(prov_mod, "urlpatterns", None)
        if prov_urlpatterns:
            provider_urlpatterns += prov_urlpatterns

    return provider_urlpatterns
