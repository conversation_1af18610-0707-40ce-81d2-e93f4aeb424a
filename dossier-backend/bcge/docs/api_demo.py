#!/usr/bin/env python

# /// script
# requires-python = ">=3.10"
# dependencies = [
#   "httpx",
#   "pyjwt",
# ]
# ///


import json
import time
import uuid
from pathlib import Path

import httpx
import jwt  # pyjwt

client_id = "bcgeevo-test-client"
# client_secret = os.getenv("HYPODOSSIER_BCGE_API_CLIENT_SECRET")

client_secret = "dl7hWj6VWkvdrtNGBayYCIfkMjr4WHyH"
environment = "bcgeevo"


realm_id = "dev-hypodossier"

# hypodossier_api_endpoint = (
#     f"https://dms.{environment}.test.hypodossier.ch/partner/bcge/api/v1"
# )

hypodossier_api_endpoint = "http://localhost:8000/partner/bcge/api/v1"

# hypodossier_auth_endpoint = "https://auth.bcge.test.hypodossier.ch/realms"
hypodossier_auth_endpoint = "https://auth.hypo.duckdns.org/realms"


access_token_path = Path.cwd() / "HYPODOSSIER_API_ACCESS_TOKEN.private"

MAX_RETRIES = 5


def get_access_token():

    print("renewing token")
    res = httpx.post(
        f"{hypodossier_auth_endpoint}/{realm_id}/protocol/openid-connect/token",
        data={
            "client_id": client_id,
            "client_secret": client_secret,
            "grant_type": "client_credentials",
        },
    )
    res.raise_for_status()

    issued_token = res.json()

    print("token refreshed", json.dumps(issued_token, indent=2))
    decoded_token = jwt.decode(
        issued_token["access_token"], options={"verify_signature": False}
    )
    print("decoded token", json.dumps(decoded_token, indent=2))
    print("storing token for later usage at", access_token_path.absolute())

    access_token_path.write_text(json.dumps(issued_token, indent=2))
    access_token_path.chmod(0o600)
    return issued_token["access_token"]


# Get the access token
access_token = get_access_token()

# Headers for authenticated requests
headers = {"Authorization": f"Bearer {access_token}"}

# Ping the API
print("Pinging the API...")
res = httpx.get(f"{hypodossier_api_endpoint}/ping", headers=headers)
res.raise_for_status()
print("Ping response:", res.json())

# Create a new dossier
print("Creating a new dossier...")
external_dossier_id = str(uuid.uuid4())
dossier_data = {
    "external_dossier_id": external_dossier_id,
    "name": "Demo Dossier",
    "lang": "fr",  # Language code, e.g., 'en', 'fr', 'de'
}
res = httpx.post(
    f"{hypodossier_api_endpoint}/dossier",
    json=dossier_data,
    headers=headers,
)
res.raise_for_status()
print("Dossier created:", res.json())

# Upload a file to the dossier
print("Uploading a file to the dossier...")
file_path = Path("./lawssimplicity.pdf")
if not file_path.exists():
    print(f"File not found: {file_path}")
    exit(1)

with file_path.open("rb") as f:
    files = {"file": (file_path.name, f, "application/pdf")}
    res = httpx.post(
        f"{hypodossier_api_endpoint}/dossier/{external_dossier_id}/original-files",
        files=files,
        headers=headers,
    )
res.raise_for_status()
print("File uploaded:", res.json())

# Get semantic documents associated with the dossier
print("Fetching semantic documents...")
res = httpx.get(
    f"{hypodossier_api_endpoint}/dossier/{external_dossier_id}/semantic-documents",
    headers=headers,
)
res.raise_for_status()
semantic_documents = res.json()
print("Semantic documents:", semantic_documents)

retry_count = 0
semantic_documents = []

# Retry loop to check for at least one semantic document
while not semantic_documents and retry_count < MAX_RETRIES:
    print(f"Fetching semantic documents... (Attempt {retry_count + 1})")

    res = httpx.get(
        f"{hypodossier_api_endpoint}/dossier/{external_dossier_id}/semantic-documents",
        headers=headers,
    )

    res.raise_for_status()
    semantic_documents = res.json()

    # Check if we have at least one semantic document
    if semantic_documents:
        print("Semantic document found:", semantic_documents[0])
        break

    retry_count += 1
    time.sleep(5)  # Wait 1 second before trying again

# You will need to wait for Original File to be processed into a Semantic Document

# Update a semantic document if available
if semantic_documents:
    semantic_document = semantic_documents[0]
    semantic_document_uuid = semantic_document["uuid"]
    print(f"Updating semantic document {semantic_document_uuid}...")
    update_data = {
        "external_semantic_document_id": "Updated External ID",
        "access_mode": "read_only",
    }
    res = httpx.patch(
        f"{hypodossier_api_endpoint}/dossier/{external_dossier_id}/semantic-documents/{semantic_document_uuid}",
        json=update_data,
        headers=headers,
    )
    res.raise_for_status()
    print("Semantic document updated:", res.json())

    # Set Semantic Document ready for export and trigger processing
    print("Exporting semantic document to PDF...")
    res = httpx.post(
        f"{hypodossier_api_endpoint}/export/dossier/{external_dossier_id}/semantic-documents/{semantic_document_uuid}/set-state-ready-for-export",
        headers=headers,
    )
    res.raise_for_status()
    export_request = res.json()
    print("Export request submitted:", export_request)

    # Check export status
    print("Checking export status...")

    retry_count = 0
    while retry_count < MAX_RETRIES:
        print(f"Checking export status... (Attempt {retry_count + 1})")

        # Make the request to check export status
        res = httpx.get(
            f"{hypodossier_api_endpoint}/export/{external_dossier_id}/semantic-documents-available",
            headers=headers,
        )

        if res.status_code != 404:
            export_status = res.json()
            if export_status != []:
                print("Export status:", export_status)
                break

        retry_count += 1
        time.sleep(1)  # Wait before trying again

    # We can also get a list of all semantic documents that are ready for export for a given account
    res = httpx.get(
        f"{hypodossier_api_endpoint}/export/all-semantic-documents-available",
        headers=headers,
    )

    print("Export status for all semantic documents in account:", res.json())

    # Set export status to complete
    print("Setting export status to complete...")
    res = httpx.post(
        f"{hypodossier_api_endpoint}/export/{external_dossier_id}/semantic-documents/{semantic_document_uuid}/set-done",
        headers=headers,
    )
    res.raise_for_status()
    export_status = res.json()
    print("Export status:", export_status)

    # Check to make sure we have no more semantic documents available for download/export
    print("Checking for more semantic documents to export...")
    # Make the request to check export status
    res = httpx.get(
        f"{hypodossier_api_endpoint}/export/{external_dossier_id}/semantic-documents-available",
        headers=headers,
    )

    res.raise_for_status()
    export_status = res.json()
    print("New Export status:", export_status)

    # Delete the semantic document
    print(f"Deleting semantic document {semantic_document_uuid}...")
    res = httpx.delete(
        f"{hypodossier_api_endpoint}/dossier/{external_dossier_id}/semantic-document/{semantic_document_uuid}",
        headers=headers,
    )
    res.raise_for_status()
    print("Semantic document deleted:", res.json())

    # Restore the semantic document
    print(f"Restoring semantic document {semantic_document_uuid}...")
    res = httpx.put(
        f"{hypodossier_api_endpoint}/dossier/{external_dossier_id}/semantic-document/{semantic_document_uuid}/restore",
        headers=headers,
    )
    res.raise_for_status()
    print("Semantic document restored:", res.json())

print("Fetching original files...")
res = httpx.get(
    f"{hypodossier_api_endpoint}/original-files/",
    headers=headers,
)
res.raise_for_status()
original_files = res.json()
print("Original files:", original_files)

# Export the original file
original_file = original_files[0]
original_file_uuid = original_file["original_file_uuid"]
print(f"Exporting original file {original_file_uuid}...")
res = httpx.get(
    f"{hypodossier_api_endpoint}/export/original-file/{original_file_uuid}",
    headers=headers,
)
res.raise_for_status()
exported_file = res.json()
print("Exported file info:", exported_file)

# Delete the dossier
print("Deleting the dossier...")
res = httpx.delete(
    f"{hypodossier_api_endpoint}/dossier/{external_dossier_id}",
    headers=headers,
)
res.raise_for_status()
print("Dossier deleted:", res.json())
