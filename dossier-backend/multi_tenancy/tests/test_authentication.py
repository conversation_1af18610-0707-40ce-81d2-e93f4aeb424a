import pytest

from core.authentication import create_token
from multi_tenancy.authentication import Multi<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from projectconfig.authentication import JWTAuthRequest

pytestmark = pytest.mark.django_db


@pytest.fixture
def auth():
    return MultiTenancyJWTAuth()


@pytest.fixture
def jwt_request():
    return JWTAuthRequest()


def test_authenticate_valid_token_with_account_key(auth, jwt_request):
    """Test authentication with valid token containing account_key"""
    token = create_token(
        "Test",
        "User",
        "<EMAIL>",
        account_key="default",
        signed_with_keycloak_key=True,
    )

    result = auth.authenticate(jwt_request, token)

    assert result is True
    assert jwt_request.jwt is not None
    assert jwt_request.jwt["account_key"] == "default"


def test_authenticate_valid_token_with_account_keys(auth, jwt_request):
    """Test authentication with valid token containing account_keys list"""
    token = create_token(
        "Test",
        "User",
        "<EMAIL>",
        extra_fields={"account_keys": ["default", "account2"]},
        signed_with_keycloak_key=True,
    )

    result = auth.authenticate(jwt_request, token)

    assert result is True
    assert jwt_request.jwt is not None
    assert jwt_request.jwt["account_keys"] == ["default", "account2"]


def test_authenticate_invalid_token(auth, jwt_request):
    """Test authentication with invalid token"""
    result = auth.authenticate(jwt_request, "invalid_token")

    assert result is False
    assert not hasattr(jwt_request, "jwt")


def test_authenticate_token_missing_account_access(auth, jwt_request):
    """Test authentication with token missing both account_key and account_keys"""
    token = create_token(
        "Test",
        "User",
        "<EMAIL>",
        extra_fields={},  # No account access fields
        signed_with_keycloak_key=True,
    )

    result = auth.authenticate(jwt_request, token)

    assert result is False
    assert not hasattr(jwt_request, "jwt")


def test_authenticate_token_with_both_account_fields(auth, jwt_request):
    """Test authentication with token containing both account_key and account_keys"""
    token = create_token(
        "Test",
        "User",
        "<EMAIL>",
        account_key="default",
        extra_fields={"account_keys": ["account2"]},
        signed_with_keycloak_key=True,
    )

    result = auth.authenticate(jwt_request, token)

    assert result is True
    assert jwt_request.jwt is not None
    assert jwt_request.jwt["account_key"] == "default"
    assert jwt_request.jwt["account_keys"] == ["account2"]
