from typing import List

from processed_file.schemas import PageObjectSchema
from semantic_document.models import SemanticDocument, SemanticPagePageObject
from semantic_document.services import (
    get_ordered_semantic_page_objects,
    filter_unique_page_objects,
)


def export_aggregate_page_objects_from_semantic_document_with_title(
    semantic_document: SemanticDocument,
    exclude_page_object_type_names: List[str] = None,
) -> List[PageObjectSchema]:

    # Similar to export_aggregate_page_objects_from_semantic_document in semantic_document/semantic_document_pdf_export.py
    # but does additional join on semantic_page__semantic_document and additional calculations for semantic_document
    # title per language as schema for ZKB is different
    sp_page_objects = get_ordered_semantic_page_objects(
        semantic_document,
        additional_select_related=[
            "semantic_page__semantic_document",
            "semantic_page__semantic_document__document_category",
            "page_object__type",
        ],
    ).filter(page_object__visible=True)

    # Do not export financial hurdles. If that changes refactor here
    # Also exclude address blocks (they have to be cleaned/extracted internally)
    sp_page_objects = sp_page_objects.exclude(
        page_object__type__name__in=["FINHURDLE", "ADDRESS_BLOCK"]
    )

    # Additional filter for page objects
    if exclude_page_object_type_names:
        sp_page_objects = sp_page_objects.exclude(
            page_object__type__name__in=exclude_page_object_type_names
        )

    # Filter so that page_object.key is unique per semantic document
    unique_page_objects = filter_unique_page_objects(sp_page_objects)

    # Sort by doc_cat_id, doc, position in doc
    sorted_page_objects = sort_page_objects(unique_page_objects)

    # Keep only page objects that should be exported
    filtered_page_objects = filter_page_objects_for_export(sorted_page_objects)
    return [
        PageObjectSchema.model_validate(prepare_page_object_schema(spo))
        for spo in filtered_page_objects
    ]


def prepare_page_object_schema(sem_po: SemanticPagePageObject) -> dict:
    # Returns JSON format compatible with PageObjectSchema
    # TODO: upon migration to pydantic v2 replace with def model_validate method on PageObjectSchema

    de_doctitle = sem_po.semantic_page.semantic_document.title_lang(lang="de")
    en_doctitle = sem_po.semantic_page.semantic_document.title_lang(lang="en")
    fr_doctitle = sem_po.semantic_page.semantic_document.title_lang(lang="fr")
    it_doctitle = sem_po.semantic_page.semantic_document.title_lang(lang="it")

    return {
        "uuid": sem_po.page_object.uuid,
        "key": sem_po.page_object.key.key,
        "visible": sem_po.page_object.visible,
        "titles": {
            "de": sem_po.page_object.key.de,
            "en": sem_po.page_object.key.en,
            "fr": sem_po.page_object.key.fr,
            "it": sem_po.page_object.key.it,
        },
        "value": sem_po.page_object.value,
        "type": sem_po.page_object.type.name,
        "bbox": {
            "ref_width": sem_po.page_object.ref_width,
            "ref_height": sem_po.page_object.ref_height,
            "top": sem_po.page_object.top,
            "left": sem_po.page_object.left,
            "right": sem_po.page_object.right,
            "bottom": sem_po.page_object.bottom,
        },
        # Important Use the page number of the semantic page, rather than the processed page
        "page_number": sem_po.semantic_page.number,
        "confidence": sem_po.page_object.confidence_value,
        "semantic_page_uuid": sem_po.semantic_page.uuid,
        "semantic_document_uuid": sem_po.semantic_page.semantic_document.uuid,
        "semantic_document_titles": {
            "de": de_doctitle,
            "en": en_doctitle,
            "fr": fr_doctitle,
            "it": it_doctitle,
        },
        "semantic_document_category_key": sem_po.semantic_page.semantic_document.document_category.name,
        "semantic_document_category_id": sem_po.semantic_page.semantic_document.document_category.id,
    }


def sort_page_objects(
    unique_page_objects: List[SemanticPagePageObject],
) -> List[SemanticPagePageObject]:

    # Sort the page objects by
    # - semantic document
    # - page number
    # - 'top' position on page
    # - 'left' position on page
    # Todo: add some tolerance for top, left (round it e.g. to 5 pixels to account for "is on the same line but not
    # pixel perfect")
    sorted_page_objects = sorted(
        unique_page_objects,
        key=lambda obj: (
            obj.semantic_page.semantic_document.document_category.id,
            obj.semantic_page.semantic_document.uuid,
            obj.semantic_page.number,
            obj.page_object.top,
            obj.page_object.left,
        ),
        reverse=False,
    )
    return sorted_page_objects


def filter_page_objects_for_export(
    po_in: List[SemanticPagePageObject],
) -> List[SemanticPagePageObject]:
    """
    Remove certain page objects from the export as they are not relevant to external users
    @param po_in: unfiltered list of page objects
    @return: list of page objects to be exported
    """
    KEYS_TO_BE_FILTERED = [
        "confirmation_empty",  # use "status" instead
        "identity_cover_misc",  # No useful information on it
    ]
    KEY_PREFIXES_TO_BE_FILTERED = ["misc_"]
    po_out = []
    for po in po_in:
        if po.page_object.key.key in KEYS_TO_BE_FILTERED:
            continue

        # page_object.key is of type PageObjectTitle
        if startswith_any(po.page_object.key.key, KEY_PREFIXES_TO_BE_FILTERED):
            continue
        po_out.append(po)
    return po_out


def startswith_any(s: str, list_of_prefixes: List[str]) -> bool:
    if s and list_of_prefixes:
        for prefix in list_of_prefixes:
            if s.startswith(prefix):
                return True
    return False
