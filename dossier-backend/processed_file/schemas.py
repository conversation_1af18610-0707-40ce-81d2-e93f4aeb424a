from typing import Optional
from uuid import UUID

from pydantic import BaseModel

from dossier.schemas import BBox


class MultilingualTitles(BaseModel):
    de: str
    en: str
    fr: str
    it: str


class PageObjectSchema(BaseModel):
    # Unique identifier of page object (internal id of page object)
    uuid: UUID

    # Stable key that identifies the content of the po
    key: str
    titles: MultilingualTitles
    value: Optional[str] = None

    # Type of page object. Options are
    # STRING, PARAGRAPH, DATE, INT, CURRENCY, OBJECT, IMAGE
    # (ADDRESS_BLOCK, FINHURDLE is filtered out)
    type: str

    bbox: BBox

    # 0-based index of semantic page in semantic document on which this po exists
    page_number: int

    # Float 0.00 - 1.00 that indicates how confident we are that this value is correct
    # Can change and be recalibrated any time but can be used to sort page objects
    confidence: float

    semantic_page_uuid: UUID
    semantic_document_uuid: UUID
    semantic_document_titles: MultilingualTitles
    semantic_document_category_key: str
    semantic_document_category_id: str

    # PG: When migrating from pydantic v1 -> v2, we can use APPROX this code instead of prepare_page_object_schema
    # model_config = ConfigDict(from_attributes=True)
    #
    # @classmethod
    # def model_validate(cls, obj: SemanticPagePageObject):
    #     return cls(
    #         uuid=str(obj.page_object.uuid),
    #         key=obj.page_object.key.key,
    #         title=obj.page_object.key.de,
    #         titles=PageObjectTitles(
    #             en=obj.page_object.key.en,
    #             de=obj.page_object.key.de,
    #             fr=obj.page_object.key.fr,
    #             it=obj.page_object.key.it,
    #         ),
    #         visible=obj.page_object.visible,
    #         value=obj.page_object.value,
    #         type=obj.page_object.type.name,
    #         bbox=BBox(
    #             ref_width=obj.page_object.ref_width,
    #             ref_height=obj.page_object.ref_height,
    #             top=obj.page_object.top,
    #             left=obj.page_object.left,
    #             right=obj.page_object.right,
    #             bottom=obj.page_object.bottom,
    #         ),
    #         page_number=(
    #             obj.semantic_page.number
    #         ),
    #         confidence=obj.page_object.confidence_value,
    #         confidence_summary=ConfidenceSummary(
    #             value=obj.page_object.confidence_value,
    #             value_formatted=obj.page_object.confidence_formatted,
    #             level=obj.page_object.confidence_level,
    #         ),
    #         semantic_page_uuid=obj.semantic_page.uuid,
    #     )
