from pathlib import Path
from tempfile import TemporaryDirectory
from typing import Union
from zipfile import ZipFile

import requests
import structlog
from channels.db import database_sync_to_async

from dossier.models import SemanticDocumentExportStrategy
from workers.schemas import (
    SemanticDocumentPDFRequestV1,
    SemanticDocumentPDFResponseV1,
)
from workers.services.semantic_document_pdf_export import (
    worker_generate_semantic_document_pdf,
    SemanticDocumentExport,
)
from workers.services.pdfa import convert_and_get_pdfa

logger = structlog.get_logger(__name__)


def process_semantic_dossier_pdf_request(
    *, semantic_document_pdf_request: Union[str, bytes, SemanticDocumentPDFRequestV1]
) -> str:
    """
    Handle accepting a semantic dossier pdf request from rabbitmq and returning the pdf
    @return: JSON string containing the response with success or error information
    """
    request_uuid = None
    try:
        if isinstance(semantic_document_pdf_request, str) or isinstance(
            semantic_document_pdf_request, bytes
        ):
            semantic_document_pdf_request = (
                SemanticDocumentPDFRequestV1.model_validate_json(
                    semantic_document_pdf_request
                )
            )
        else:
            semantic_document_pdf_request = semantic_document_pdf_request

        request_uuid = semantic_document_pdf_request.semantic_document_pdf_request_uuid
        logger.info(f"processing semantic document pdf request {request_uuid}")

        try:
            with TemporaryDirectory() as temp_dir:
                temp_path = Path(temp_dir)

                semantic_document_export: SemanticDocumentExport = (
                    worker_generate_semantic_document_pdf(
                        semantic_dossier=semantic_document_pdf_request.semantic_dossier,
                        semantic_document_uuid=semantic_document_pdf_request.semantic_document_uuid,
                        dest_path=temp_path,
                        add_metadata=semantic_document_pdf_request.add_metadata,
                        add_uuid_suffix=semantic_document_pdf_request.add_uuid_suffix,
                    )
                )

                if semantic_document_pdf_request.enable_pdfa_conversion_for_export:
                    response_content = convert_and_get_pdfa(
                        document_path=semantic_document_export.path,
                        document_uuid=str(
                            semantic_document_pdf_request.semantic_document_uuid
                        ),
                    )

                    # Overwrite the pdf file with the pdfa content
                    with open(semantic_document_export.path, "wb") as f:
                        f.write(response_content)

                if (
                    semantic_document_pdf_request.semantic_document_export_strategy
                    == SemanticDocumentExportStrategy.SWISSCOM_EDOSSIER_XML_ZIP
                    and semantic_document_pdf_request.metadata
                ):
                    # Create a zip file with the pdf and metadata
                    metadata: bytes = semantic_document_pdf_request.metadata.to_xml()
                    # Save metadata to XML file in temp directory
                    metadata_path = (
                        temp_path / f"{semantic_document_export.path.stem}.xml"
                    )
                    metadata_path.write_bytes(metadata)

                    # Create zip file in temp directory
                    zip_path = temp_path / f"{semantic_document_export.path.stem}.zip"
                    with ZipFile(zip_path, "w") as zip_file:
                        # Add PDF file
                        zip_file.write(
                            semantic_document_export.path,
                            semantic_document_export.path.name,
                        )
                        # Add metadata file
                        zip_file.write(metadata_path, metadata_path.name)

                    # Upload the zip file
                    with zip_path.open("rb") as fp:
                        # at this point the put_upload_url is used to set the filename....
                        # the filename that comes from "fp" is irrelevant, as only the bytes from that file are used and not the name
                        response = requests.put(
                            semantic_document_pdf_request.put_upload_url, data=fp.read()
                        )
                        response.raise_for_status()

                elif ".zip" in semantic_document_pdf_request.put_upload_url:
                    logger.exception(
                        "zip file requested but either wrong strategy or no metadata provided",
                        semantic_document_export_strategy=semantic_document_pdf_request.semantic_document_export_strategy,
                        metadata=semantic_document_pdf_request.metadata,
                    )

                    raise ValueError(
                        "zip file requested but either wrong strategy or no metadata provided"
                    )

                else:

                    # Upload standard pdf file
                    with semantic_document_export.path.open("rb") as fp:
                        response = requests.put(
                            semantic_document_pdf_request.put_upload_url, data=fp.read()
                        )
                        response.raise_for_status()

            logger.info(
                "finished processing semantic_document_pdf_request request",
                request_uuid=request_uuid,
                upload_url=semantic_document_pdf_request.put_upload_url,
            )

            return SemanticDocumentPDFResponseV1(
                semantic_document_pdf_request_uuid=request_uuid, error=None
            ).model_dump_json()

        except Exception as e:
            error_msg = (
                "could not create offline package for semantic_document_pdf_request"
            )
            logger.exception(
                error_msg,
                request_uuid=request_uuid,
            )
            return SemanticDocumentPDFResponseV1(
                semantic_document_pdf_request_uuid=request_uuid,
                error=f"{error_msg}: {str(e)}",
            ).model_dump_json()

    except Exception as e:
        error_msg = "could not process message"
        logger.exception(error_msg, exc_info=True)
        return SemanticDocumentPDFResponseV1(
            semantic_document_pdf_request_uuid=request_uuid,
            error=f"{error_msg}: {str(e)}",
        ).model_dump_json()


async def async_process_semantic_dossier_pdf_request(
    *, semantic_document_pdf_request: Union[str, bytes, SemanticDocumentPDFRequestV1]
) -> str:
    return await database_sync_to_async(process_semantic_dossier_pdf_request)(
        semantic_document_pdf_request=semantic_document_pdf_request
    )
