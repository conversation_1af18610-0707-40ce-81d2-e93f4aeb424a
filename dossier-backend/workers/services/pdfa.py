import uuid
from pathlib import Path
from time import time
from typing import Optional
import platform
import subprocess
import tempfile

import aio_pika
import requests
import structlog

from actorizer import Broker as PersistentBrokerConnection
from aio_pika.patterns import RPC
from asgiref.sync import async_to_sync
from pydantic import HttpUrl
from requests import Response
from tenacity import (
    retry,
    stop_after_attempt,
    wait_exponential,
    retry_if_exception_type,
)

from django.conf import settings
from projectconfig.settings import (
    HYPYFREP_PDFA_CONVERT_REQUEST_ROUTING_KEY,
    HYPYFREP_PDFA_CONVERT_RETRY_COUNT,
)
from tempfilestore.filestore import upload_file_and_get_presigned_url

from workers.schemas import FREPPDFAConversionResponse, FREPPDFAConversionRequest

logger = structlog.get_logger(__name__)


class PDFError(Exception):
    """Base class for PDF-related errors."""

    pass


class PDFRepairError(PDFError):
    """Exception raised when PDF repair fails."""

    pass


def is_pdftocairo_available() -> bool:
    """Check if pdftocairo is available on the system."""
    try:
        subprocess.run(
            ["pdftocairo", "-v"],
            capture_output=True,
            check=True,
        )
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        return False


def process_pdftocairo_pdf_fix(
    local_file: Path, output_path_dir: Path, suffix: str = "_repairedcairo"
) -> Path:
    """
    Try to fix a potentially broken PDF file using pdftocairo.

    Args:
        local_file: Path to the source PDF file
        output_path_dir: Directory to save the repaired file
        suffix: Suffix to add to the repaired file name

    Returns:
        Path to repaired file

    Raises:
        PDFRepairError: If repair fails
    """
    if not is_pdftocairo_available():
        raise PDFRepairError("pdftocairo is not available")

    dest_path = output_path_dir / f"{local_file.stem}{suffix}{local_file.suffix}"

    try:
        subprocess.run(
            ["pdftocairo", "-pdf", str(local_file), str(dest_path)],
            capture_output=True,
            text=True,
            check=True,
        )
        return dest_path

    except subprocess.CalledProcessError as e:
        error_msg = f"PDF repair failed: {e.stderr}"
        logger.error(
            error_msg,
            return_code=e.returncode,
            stdout=e.stdout,
            stderr=e.stderr,
        )
        raise PDFRepairError(error_msg) from e


def convert_and_get_pdfa(
    document_path: Path,
    document_uuid: str,
    timeout: int = 30,
) -> Optional[bytes]:
    """
    Convert a PDF document to PDF/A format and download the result.
    Attempts to repair the PDF using pdftocairo before conversion if needed.

    Args:
        document_path (str): Path to the source PDF document
        document_uuid (str): UUID of the semantic document
        timeout (int): Timeout in seconds for the download request

    Returns:
        Optional[bytes]: The downloaded PDF/A content if conversion is enabled,
                        None if conversion is disabled

    Raises:
        PDFRepairError: If PDF repair fails
        Exception: If PDF/A conversion fails
        requests.exceptions.RequestException: If download fails
    """
    start = time()
    repaired_path = None
    response_convert_pdf_to_pdfa = None

    try:
        # First try with repair
        try:
            logger.info("Attempting repair and conversion")

            # Create a named temporary file for the repaired PDF
            with tempfile.NamedTemporaryFile(suffix=".pdf") as tmp_file:
                repaired_path = Path(tmp_file.name)

                # Attempt repair
                repaired_path = process_pdftocairo_pdf_fix(
                    document_path,
                    repaired_path.parent,
                    suffix="",  # No suffix needed since we're using a temporary file
                )
                logger.info("Repair succeeded, attempting conversion")
                response_convert_pdf_to_pdfa = convert_pdf_to_pdfa_with_retry(
                    repaired_path,  # Use the repaired path for conversion
                    document_uuid,
                )
        except Exception as e:
            logger.info(
                "Attempting repair and conversion failed, trying original file",
                error=str(e),
            )
            # If repair fails, try with original file
            response_convert_pdf_to_pdfa = convert_pdf_to_pdfa_with_retry(
                document_path,
                document_uuid,
            )

        if not response_convert_pdf_to_pdfa or response_convert_pdf_to_pdfa.error:
            raise Exception(
                f"Error converting pdf to pdfa: {response_convert_pdf_to_pdfa.error if response_convert_pdf_to_pdfa else 'No response'}"
            )

        # Download the pdfa file
        response: Response = requests.get(
            response_convert_pdf_to_pdfa.data.file_url, timeout=timeout
        )
        response.raise_for_status()

        end = time()
        logger.info("convert_and_download_pdfa processed", duration=end - start)

        return response.content

    except Exception as cleanup_error:
        logger.warning("Failed to clean up repaired PDF", error=str(cleanup_error))


async def dispatch_rpc_request(routing_key: str, request: dict) -> str:
    connection = await aio_pika.connect_robust(
        f"{settings.RABBIT_URL}?name=caller workers {platform.node()}",
        client_properties={"connection_name": "caller"},
    )
    async with connection:
        channel = await connection.channel()
        rpc = await RPC.create(channel)

        # Creates tasks by proxy object
        response_str = await rpc.call(
            routing_key,
            request,
        )
    return response_str


async def rpc_convert_pdf_to_pdfa(
    temp_path: Path, semantic_document_uuid: str
) -> FREPPDFAConversionResponse:

    source_url: HttpUrl = await upload_file_and_get_presigned_url(temp_path)

    correlation_uuid = uuid.uuid4()

    logger.info(
        "Starting rpc_convert_pdf_to_pdfa",
        semantic_document_uuid=semantic_document_uuid,
        correlation_uuid=correlation_uuid,
    )

    conversion_request = FREPPDFAConversionRequest(
        correlation_uuid=correlation_uuid,
        source_file_url=source_url,
    )

    broker_connection = PersistentBrokerConnection(
        url=f"{settings.RABBIT_URL}?name=pdfa {platform.node()}",
    )

    await broker_connection.start()

    response: FREPPDFAConversionResponse = await broker_connection.rpc(
        routing_key=HYPYFREP_PDFA_CONVERT_REQUEST_ROUTING_KEY,
        message=conversion_request,
        expected_response=FREPPDFAConversionResponse,
    )

    logger.info(
        "Finsihed rpc_convert_pdf_to_pdfa",
        rpc_response=response,
    )

    await broker_connection.stop()

    return response


@retry(
    stop=stop_after_attempt(HYPYFREP_PDFA_CONVERT_RETRY_COUNT),
    wait=wait_exponential(multiplier=1, min=4, max=60),
    retry=retry_if_exception_type(Exception),
    reraise=True,
)
def convert_pdf_to_pdfa_with_retry(path, semantic_document_uuid):
    return async_to_sync(rpc_convert_pdf_to_pdfa)(path, str(semantic_document_uuid))
