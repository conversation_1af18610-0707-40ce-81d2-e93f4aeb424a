import djclick as click
import structlog
from django.contrib.auth import get_user_model
from faker import Faker

from dossier.models import Account
from vz.factories import VZAccountFactoryFaker, load_vz_document_categories
from vz.schemas.schemas import AccountName

User = get_user_model()
logger = structlog.get_logger()
ENABLED_ACCOUNT_KEYS = AccountName._member_map_.values()


@click.group()
def grp():
    pass


@grp.command()
@click.argument("account_key")
def load_account(
    account_key: str,
):
    """
    Create or update a vz account with close to production config. This loads
    document categories.

    Example:

    python manage.py reset-db
    python manage.py load_vz_data load-account vz
    python manage.py load_vz_data load-account vztest

    """
    Faker.seed(234777)
    bfac = VZAccountFactoryFaker(
        account_key=account_key,
    )
    bfac.account.save()


@grp.command()
@click.argument("account_key", required=False)
def update_document_categories(
    account_key: str | None = None,
):
    """
    Load / update all document categories for VZ. Do not rely on the factory here
    as the factory changes the account itself on initialization.

    python manage.py load_vz_data update-document-categories vz

    @param account_key: One from vz.schemas.schemas.AccountName. Either 'vz' or 'vztest'
    @return:
    """

    if account_key not in ENABLED_ACCOUNT_KEYS:
        raise ValueError(f"account_key must be one of {ENABLED_ACCOUNT_KEYS}")

    try:
        Account.objects.get(key=account_key)
    except Account.DoesNotExist:
        logger.error(f"Account {account_key} does not exist.")
        return

    load_vz_document_categories(account_key)
