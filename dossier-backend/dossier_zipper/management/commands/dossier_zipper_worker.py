import asyncio
import logging
import structlog
import platform

from dossier_zipper.workers import (
    async_process_dossier_zip_request,
)

import djclick as click


from core.helpers import fabric_start_server

from projectconfig.settings import ASYNC_DOSSIER_ZIPPER_WORKER_ROUTING_KEY

logger = structlog.get_logger()


async def start_server(loop):
    # Note: Has been refactored into separate app called workers
    # Kept here as part of the migration process

    client_properties = {"connection_name": f"dossier_zipper_{platform.node()}"}

    return await fabric_start_server(
        async_process_dossier_zip_request,  # Change to list of callback functions
        ASYNC_DOSSIER_ZIPPER_WORKER_ROUTING_KEY,  # Change to list of routing functions
        loop=loop,
        client_properties=client_properties,
        auto_delete=False,
        name="dossier_zipper",
    )


@click.command()
def start():
    logging.basicConfig(level=logging.INFO)

    logger.info("starting the server")
    loop = asyncio.get_event_loop()
    connection = loop.run_until_complete(start_server(loop))
    logger.info("consumer started")
    try:
        loop.run_forever()
    finally:
        loop.run_until_complete(connection.close())
