from typing import Optional
from uuid import UUID

from pydantic import BaseModel

from dossier import schemas as dossier_schemas


class DossierZipResponseV1(BaseModel):
    zip_request_uuid: UUID


class DossierZipRequestV1(BaseModel):
    zip_request_uuid: UUID
    semantic_dossier: dossier_schemas.SemanticDossier
    put_upload_url: str
    with_document_browser_viewer: Optional[bool] = False
    # From account.document_download_ui_add_uuid_suffix
    # Added a UUID suffix to the pdf file name inside the zip file
    add_uuid_suffix: Optional[bool] = False
    add_metadata_json: Optional[bool] = True
    enable_pdfa_conversion_for_export: Optional[bool] = False
