from typing import <PERSON><PERSON>
from uuid import uuid4, UUID
import platform

import aio_pika
from aio_pika.patterns import RPC
from asgiref.sync import sync_to_async
from django.conf import settings
from django.utils import timezone

from core.helpers import remove_invalid_chars
from dossier.helpers import create_dossier_file_without_saving
from dossier.helpers_filename import create_filenames_unique
from dossier.helpers_v2 import prepare_semantic_dossier
from dossier.models import DossierExport, Dossier
from dossier.schemas import SemanticD<PERSON>ier, DateRange
from dossier_zipper.schemas import DossierZipResponseV1, DossierZipRequestV1
from projectconfig.settings import ASYNC_DOSSIER_ZIPPER_WORKER_ROUTING_KEY


@sync_to_async
def get_dossier_data(
    dossier_uuid: UUID,
    date_range: DateRange = None,
) -> Tuple[SemanticDossier, Dossier]:
    dossier: Dossier = Dossier.objects.get(uuid=dossier_uuid)

    semantic_dossier = prepare_semantic_dossier(
        dossier,
        hide_empty_semantic_documents=True,
        date_range=date_range,
        include_annotations=True,
    )
    return semantic_dossier, dossier


@sync_to_async
def save_dossier_export(dossier_export: DossierExport):
    dossier_export.save()


async def generate_offline_dossier(
    dossier_uuid: UUID,
    add_uuid_suffix: bool = False,
    add_metadata_json: bool = True,
    date_range: DateRange = None,
) -> DossierExport:
    dossier: Dossier
    data: SemanticDossier
    data, dossier = await get_dossier_data(
        dossier_uuid=dossier_uuid, date_range=date_range
    )
    data: dict = data.model_dump()
    data["uuid"] = str(data["uuid"])

    dossier_export = DossierExport()

    # Honestly probably a bad design - DossierFile should only be saved after successful creation in s3
    # as parts of our codebase assume that s3 contents always exist
    dossier_file = create_dossier_file_without_saving(
        dossier, f"{remove_invalid_chars(dossier.name)}.zip"
    )

    await sync_to_async(dossier_file.save)()
    dossier_export.uuid = uuid4()
    dossier_export.dossier = dossier
    dossier_export.file = dossier_file
    await sync_to_async(dossier_export.save)()

    data["semantic_documents"] = create_filenames_unique(data["semantic_documents"])

    for i in range(len(data["semantic_documents"])):
        data["semantic_documents"][i]["uuid"] = str(
            data["semantic_documents"][i]["uuid"]
        )

    request_dossier = SemanticDossier(**data)

    request = DossierZipRequestV1(
        zip_request_uuid=dossier_export.uuid,
        semantic_dossier=request_dossier,
        put_upload_url=dossier_file.put_url,
        add_uuid_suffix=add_uuid_suffix,
        add_metadata_json=add_metadata_json,
        enable_pdfa_conversion_for_export=dossier.account.enable_pdfa_conversion_for_export,
    )

    await get_dossier(request)
    dossier_export.done = timezone.now()
    await save_dossier_export(dossier_export)
    return dossier_export


async def get_dossier(request: DossierZipRequestV1) -> DossierZipResponseV1:
    connection = await aio_pika.connect_robust(
        f"{settings.RABBIT_URL}?name=caller {platform.node()}",
        client_properties={"connection_name": "caller"},
    )
    async with connection:
        channel = await connection.channel()
        rpc = await RPC.create(channel)

        # Creates tasks by proxy object
        dossier_zip_response_str = await rpc.call(
            ASYNC_DOSSIER_ZIPPER_WORKER_ROUTING_KEY,
            dict(dossier_zip_request=request.model_dump_json()),
        )

        dossier_zip_response = DossierZipResponseV1.model_validate_json(
            dossier_zip_response_str
        )

        return dossier_zip_response
