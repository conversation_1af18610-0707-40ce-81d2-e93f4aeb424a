version: '3.8'
services:
  dms:
    image: registry.gitlab.com/hypodossier/document-universe/dossier-manager-server:${TAG-latest}
    command: sh -c "cat /root/.ssh/id_rsa.pub >> /root/.ssh/authorized_keys && mkdir -p /run/sshd && env >> /etc/environment &&  /usr/sbin/sshd -D"
    volumes:
      # Set USERNAME as env on your local system
       - /home/<USER>/.ssh/id_rsa.pub:/root/.ssh/id_rsa.pub:ro
      #- /home/<USER>/dossier_test_exchange:/app/dossier_test_exchange
    networks:
      dev_core-services:
      dev_caddy:
    ports:
      - "22002:22"
    labels:
      caddy: dms.hypo.duckdns.org
      caddy.reverse_proxy: "{{upstreams 8000}}"
      caddy.import: tls


networks:
  dev_caddy:
    external: true
  dev_core-services:
    external: true
