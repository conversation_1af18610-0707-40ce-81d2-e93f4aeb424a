import structlog
from django.db import transaction

from dossier.models import Account, <PERSON><PERSON><PERSON>
from statemgmt import STATEMGMT_PATH
from statemgmt.models import StateMachine

PATH_DOSSIER_STATE_MACHINE_EXPORT = (
    STATEMGMT_PATH / "configurations/default/default_state_machine_2025_02_27.json"
)

DOSSIER_STATE_MACHINE_NAME = "Default Dossier State Machine"

logger = structlog.get_logger()


def initialize_dossier_work_status(account: Account):
    state_machine: StateMachine = account.active_work_status_state_machine

    if state_machine is None:
        logger.info(f"State machine not found for account_key '{account.key}'")
        return

    # Get initial state
    open_state = state_machine.start_status

    # Update dossiers without state
    with transaction.atomic():
        updated_count = Dossier.objects.filter(
            account=account, work_status__isnull=True
        ).update(work_status=open_state)
        logger.info(
            f"Dossier initialisation complete. {updated_count} dossiers were updated."
        )
