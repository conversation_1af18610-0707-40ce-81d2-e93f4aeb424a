from datetime import datetime
from typing import List

import structlog
from ninja import Router

from core.schema import Message
from dossier.schemas import FileBasics, PageBasics
from processed_file.models import ProcessedFile
from semantic_document.models import SemanticPage

internal_router = Router()

logger = structlog.getLogger(__name__)


@internal_router.get(
    "/exporter/filebasics",
    response={200: List[FileBasics], 401: Message},
)
def get_file_basics(
    request,
    start: str = None,
    end: str = None,
    account_key: str = None,
    dossier_uuid: str = None,
    allow_deleted: bool = True,
):
    """

    @param request:
    @param start: date in format '********'
    @param end:  date in format '********'
    @return:
    """
    if not request.is_internal:
        return 401, {"detail": "Unauthorized"}

    start_date = datetime.strptime(start, "%Y%m%d").date()
    end_date = datetime.strptime(end, "%Y%m%d").date()

    qs = ProcessedFile.objects.filter(
        created_at__gte=start_date, created_at__lt=end_date
    ).order_by("created_at")
    if account_key:
        qs = qs.filter(dossier__account__key=account_key)

    if dossier_uuid:
        qs = qs.filter(dossier__uuid=dossier_uuid)

    processed_files = qs.all()

    file_basics: List[FileBasics] = []
    for processed_file in processed_files:
        pages: List[PageBasics] = []
        for processed_page in processed_file.processed_pages.all():
            if allow_deleted:
                use_pp = True
            else:
                use_pp = SemanticPage.objects.filter(
                    processed_page=processed_page, deleted_at__isnull=True
                ).exists()

            if use_pp:
                pages.append(
                    PageBasics(
                        number=processed_page.number,
                        searchable_pdf_url=processed_page.searchable_pdf.fast_url,
                        image_url=processed_page.image.fast_url,
                    )
                )
        if pages:
            file_basics.append(
                FileBasics(
                    pages=pages,
                    source=processed_file.dossier.account.key,
                    file_url=processed_file.extracted_file.file.fast_url,
                )
            )
    return file_basics
