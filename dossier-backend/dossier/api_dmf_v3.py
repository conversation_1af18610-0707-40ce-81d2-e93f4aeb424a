from typing import List
from uuid import UUID

import structlog
from django.contrib.auth import get_user_model
from django.db import transaction
from django.shortcuts import get_object_or_404
from ninja import Router
from ninja.errors import HttpError
from pydantic import ValidationError as PydanticValidationError

from clientis.schemas.schemas import ClientisExternalId
from core.schema import Error
from dossier import services
from dossier.doc_cat_helpers import UNKNOWN_DOCUMENT_CATEGORY_KEYS
from dossier.helpers import validate_expiry_date
from dossier.helpers_access_check import (
    get_dossier_from_request_with_access_check,
    get_writable_dossier_from_request_with_access_check,
)
from dossier.models import (
    UserInvolvement,
    DossierUser,
    BusinessCaseType,
    OriginalFile,
    SemanticDocumentExportStrategy,
)
from dossier.schemas_dmf_v3 import (
    FinHurdleGroup,
    DossierPropertiesResponse,
    DossierPropertiesChange,
    FileExceptionSchema,
    ExtractedFileSchema,
    OriginalFileSchema,
    DossierProcessingDetails,
    SemanticDocumentPDFExportRequest,
)
from dossier.services_external import create_dossier_state_context
from dossier.services_dmf_v3 import (
    export_page_objects_for_hurdle_view,
    assign_user_to_dossier,
    format_dossier_properties_schema,
)
from semantic_document.models import SemanticDocument
from semantic_document.services import (
    set_semantic_documents_ready_for_export,
)
from statemgmt.configurations.semantic_document_state_machine import (
    SemanticDocumentState,
)
from statemgmt.models import Status

# API router implementing component based views for to dossier management frontend
# I.e. api's here are tightly coupled to javascript dossier management components and are designed to be
# consumed by react query within components
dmf_v3_router = Router()

logger = structlog.getLogger(__name__)

User = get_user_model()


@dmf_v3_router.get("/hurdles/dossier/{dossier_uuid}", response=List[FinHurdleGroup])
def get_dossier_hurdles(request, dossier_uuid: UUID):
    dossier = get_dossier_from_request_with_access_check(request, dossier_uuid)
    return export_page_objects_for_hurdle_view(dossier=dossier)


# https://gitlab.com/hypodossier/document-universe/-/issues/474
@dmf_v3_router.get(
    "/dossier/{dossier_uuid}/dossier-properties",
    response=DossierPropertiesResponse,
    url_name="dossier-properties",
)
def get_dossier_properties(request, dossier_uuid: UUID):
    dossier = get_dossier_from_request_with_access_check(request, dossier_uuid)

    involvement = (
        UserInvolvement.objects.filter(dossier=dossier, role__key="ASSIGNEE")
        .select_related("role", "user__user")
        .first()
    )

    return format_dossier_properties_schema(dossier=dossier, involvement=involvement)


@dmf_v3_router.patch(
    "/{dossier_uuid}/change-dossier-properties-v2",
    response={200: DossierPropertiesResponse, 400: Error},
    url_name="change-dossier-properties-v2",
)
def change_dossier_properties_v2(
    request, dossier_uuid: UUID, dossier_change: DossierPropertiesChange
):
    # Same as V1 API, but correctly handles updating assigned user to dossier
    dossier = get_writable_dossier_from_request_with_access_check(request, dossier_uuid)

    username = request.auth.user.username

    logger.info(
        "change_dossier_properties_v2",
        dossier_uuid=dossier_uuid,
        dossier_change=dossier_change,
        username=username,
    )

    with transaction.atomic():
        if dossier_change.assignee_username:
            dossier_user = get_object_or_404(
                DossierUser,
                account=dossier.account,
                user__username=dossier_change.assignee_username,
            )

            roles_to_update = dossier_change.dossier_role
            role_list = (
                roles_to_update.split(",")
                if roles_to_update is not None and len(roles_to_update) > 0
                else []
            )
            old_owner = dossier.owner
            assign_user_to_dossier(
                dossier=dossier,
                dossier_user=dossier_user,
                dossier_roles=role_list,
            )
            new_owner = dossier.owner

            if old_owner != new_owner:
                logger.info(
                    "DOSSIER_CHANGE: dossier owner has changed",
                    dossier_uuid=dossier.uuid,
                    dossier_user=dossier_user,
                    old_owner=old_owner,
                    new_owner=new_owner,
                    dossier_roles=role_list,
                    username=username,
                )

        if dossier_change.dossier_name:
            if dossier.name != dossier_change.dossier_name:
                logger.info(
                    "DOSSIER_CHANGE: dossier name has changed",
                    dossier_uuid=str(dossier.uuid),
                    old_dossier_name=dossier.name,
                    new_dossier_name=dossier_change.dossier_name,
                    username=username,
                )

                dossier.name = dossier_change.dossier_name

        if dossier_change.expiry_date:
            if validate_expiry_date(dossier_change.expiry_date):
                if dossier_change.expiry_date != dossier.expiry_date:
                    logger.info(
                        "DOSSIER_CHANGE: expiry_date has changed",
                        dossier_uuid=str(dossier.uuid),
                        d_expiry_date=dossier.expiry_date,
                        d_change_date=dossier_change.expiry_date,
                        username=username,
                    )
                    dossier.expiry_date = dossier_change.expiry_date

        if dossier_change.businesscase_type_uuid is not None:
            new_businesscase_type = get_object_or_404(
                BusinessCaseType, uuid=dossier_change.businesscase_type_uuid
            )
            if new_businesscase_type != dossier.businesscase_type:
                logger.info(
                    "DOSSIER_CHANGE: businesscase_type has changed",
                    dossier_uuid=str(dossier.uuid),
                    old_businesscase_type=dossier.businesscase_type,
                    new_businesscase_type=new_businesscase_type,
                    username=username,
                )

                dossier.businesscase_type = new_businesscase_type

        if dossier_change.work_status_uuid is not None:
            # Current frontend has no field to change the work_status where this is called
            # so this should never apply
            if dossier_change.work_status_uuid != dossier.work_status_id:
                context = create_dossier_state_context(
                    dossier=dossier, is_user=True, is_system=False
                )
                new_work_status = get_object_or_404(
                    Status, uuid=dossier_change.work_status_uuid
                )
                logger.info(
                    "DOSSIER_CHANGE: work_status has changed",
                    dossier_uuid=str(dossier.uuid),
                    old_work_status=dossier.work_status,
                    new_work_status=new_work_status,
                    context=context,
                    username=username,
                )
                services.change_dossier_work_status(dossier, context, new_work_status)

        dossier.save()

    return format_dossier_properties_schema(
        dossier=dossier,
        involvement=UserInvolvement.objects.filter(
            dossier=dossier, role__key="ASSIGNEE"
        ).first(),
    )


# API originally created as part of https://gitlab.com/hypodossier/document-universe/-/merge_requests/796
# Ended up going with a simpler solution, but this API might still be used at a later date if we decided to break
# down the datav2 api into smaller parts
# Not used as of 2024-08-02
@dmf_v3_router.get(
    "/dossier/{dossier_uuid}/processing-details",
    response=DossierProcessingDetails,
    url_name="dossier-processing-details",
    description="Get processing details for a dossier, used to display processing errors in the frontend",
)
def get_dossier_processing_details(request, dossier_uuid: UUID):

    dossier = get_dossier_from_request_with_access_check(request, dossier_uuid)

    original_files = (
        OriginalFile.objects.filter(dossier=dossier)
        .prefetch_related(
            "extractedfile_set",
            "extractedfile_set__file",
            "extractedfile_set__fileexception",
        )
        .select_related("file")
        .order_by("created_at", "updated_at")
    )

    original_files_list = []

    for original_file in original_files:
        extracted_files_list = []

        for extracted_file in original_file.extractedfile_set.all().order_by(
            "created_at", "updated_at"
        ):
            # Check if extracted file has a file exception
            if hasattr(extracted_file, "fileexception"):
                file_exception = FileExceptionSchema(
                    type=extracted_file.fileexception.type,
                    hypo_dossier_exception=extracted_file.fileexception.exception_type,
                    exception_en=extracted_file.fileexception.en,
                    exception_de=extracted_file.fileexception.de,
                    exception_fr=extracted_file.fileexception.fr,
                    exception_it=extracted_file.fileexception.it,
                )
            else:
                file_exception = None

            extracted_file_schema = ExtractedFileSchema(
                uuid=extracted_file.uuid,
                path_from_original=extracted_file.path_from_original,
                status=extracted_file.status,
                file_name=extracted_file.file.name,
                exception=file_exception,
            )

            extracted_files_list.append(extracted_file_schema)

        original_file_schema = OriginalFileSchema(
            uuid=original_file.uuid,
            file_name=original_file.file.name,
            status=original_file.status.upper(),
            exception_en=original_file.exception_en,
            exception_de=original_file.exception_de,
            exception_fr=original_file.exception_fr,
            exception_it=original_file.exception_it,
            extracted_files=extracted_files_list,
        )

        original_files_list.append(original_file_schema)

    processing_details = DossierProcessingDetails(
        dossier_uuid=dossier.uuid,
        original_files=original_files_list,
    )

    return processing_details


@dmf_v3_router.post(
    "dossier/{dossier_uuid}/set-semantic-documents-state-ready-for-export",
    url_name="dossier-set-semantic-documents-state-ready-for-export",
    description="Change the state of all semantic documents inside a dossier to ready for export, and dispatch export process. "
    "Will only process documents that have SemanticDocument.work_state=IN_FRONT_OFFICE",
    response=SemanticDocumentPDFExportRequest,
)
def set_semantic_documents_state_ready_for_export(request, dossier_uuid: UUID):

    # We decide that the dossier does NOT need to be writable.
    dossier = get_dossier_from_request_with_access_check(request, dossier_uuid)

    # account

    if (
        dossier.account.semantic_document_export_strategy
        == SemanticDocumentExportStrategy.SWISSCOM_EDOSSIER_XML_ZIP
    ):
        external_id = dossier.external_id
        try:
            # Validate external id matches required schema
            ClientisExternalId(external_id=external_id)
        except PydanticValidationError as e:
            raise HttpError(
                422,
                f"Invalid external ID format: {str(e)}. Expected format is bank_id + '.' + client_id + '.' + framecontract_id + '.' + 'application_id', e.g. '949.401678.001.110794'",
            )

    semantic_documents = SemanticDocument.objects.filter(
        dossier=dossier,
        work_status=dossier.account.active_semantic_document_work_status_state_machine.start_status,
    )

    semantic_document_processing_request_uuids = (
        set_semantic_documents_ready_for_export(semantic_documents)
    )

    return SemanticDocumentPDFExportRequest(
        semantic_document_export_request_uuids=semantic_document_processing_request_uuids
    )


@dmf_v3_router.get(
    "dossier/{dossier_uuid}/get-semantic-documents-target-state-ready-for-export",
    url_name="get-semantic-documents-target-state-ready-for-export",
    description="Get a list of semantic documents that can be set ready for export",
    response=List[UUID],
)
def get_semantic_documents_ready_for_export(request, dossier_uuid: UUID):
    dossier = get_dossier_from_request_with_access_check(request, dossier_uuid)

    semantic_documents = SemanticDocument.objects.filter(
        work_status__key=SemanticDocumentState.IN_FRONT_OFFICE.value,
        work_status__state_machine=dossier.account.active_semantic_document_work_status_state_machine,
        dossier=dossier,
    )

    if dossier.account.enable_semantic_document_export_unknown_documents is False:
        semantic_documents = semantic_documents.exclude(
            document_category__name__in=UNKNOWN_DOCUMENT_CATEGORY_KEYS
        )

    return [semantic_document.uuid for semantic_document in semantic_documents]
