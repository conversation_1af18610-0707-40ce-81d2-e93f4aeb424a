from typing import List
from uuid import UUID

import ninja
from django.shortcuts import get_object_or_404

import core.schema
from doccheck import schemas
from doccheck.models import Person
from doccheck import services as doccheck_services
from dossier.helpers_access_check import get_dossier_from_request_with_access_check
from dossier.models import Dossier

router = ninja.Router()


def check_case_access(request, case_uuid: UUID):
    dqs = Dossier.objects.filter(doccheck_case__uuid=case_uuid)

    # Fetch dossier explicitly to make sure we have a dossier_uuid parameter (and only one dossier)
    dossier = get_object_or_404(dqs)

    dossier = get_dossier_from_request_with_access_check(
        request, dossier_uuid=dossier.uuid
    )
    return dossier.doccheck_case


@router.get(
    "/case/{case_uuid}",
    response={200: schemas.CaseDetailOut, 404: core.schema.Message},
    url_name="case",
)
def get_case(request, case_uuid: UUID):
    case = check_case_access(request, case_uuid)
    return 200, doccheck_services.map_case_detail_out(case)


@router.put(
    "/case/{case_uuid}",
    response={201: schemas.CaseOut, 404: core.schema.Message},
    url_name="case",
)
def update_case(request, case_uuid: UUID, case_to_update: schemas.CaseUpdate):
    """Updates the provided case with the provided attributes in the request body.
    Needs to be triggered in the front-end when changing the drop-down "Business case type" in the tab "Completeness".
    """
    case = check_case_access(request, case_uuid)
    case = doccheck_services.update_case(case, case_to_update)
    return 201, schemas.CaseOut(
        uuid=case.uuid, business_case_type=case.business_case_type.key
    )


@router.put(
    "/case/{case_uuid}/persons/{person_uuid}",
    response={
        201: schemas.PersonOut,
        403: core.schema.Message,
        404: core.schema.Message,
    },
    url_name="case_person",
)
def update_person(
    request, case_uuid: UUID, person_uuid: UUID, person_to_update: schemas.PersonUpdate
):
    """Updates the provided person with the provided attributes in the request body.
    Needs to be triggered in the front-end when changing any field of a borrower in the tab "Completeness".
    Provide the key value pair of an attribute with the key of the changed field and the key of the dropdown value
    of the field or True/False for boolean fields.
    """
    case = check_case_access(request, case_uuid)
    person = doccheck_services.update_person(case, person_uuid, person_to_update)
    return 201, doccheck_services.map_person_out(0, person)


@router.post(
    "/case/{case_uuid}/persons",
    response={201: schemas.PersonOut, 404: core.schema.Message},
    url_name="case_persons",
)
def add_person(request, case_uuid: UUID):
    """Adds a person the provided case with default values."""
    case = check_case_access(request, case_uuid)
    person = doccheck_services.add_person_to_case(case, schemas.PersonCreate())
    return 201, doccheck_services.map_person_out(0, person)


@router.delete(
    "/case/{case_uuid}/persons/{person_uuid}",
    response={
        204: core.schema.Message,
        403: core.schema.Message,
        404: core.schema.Message,
    },
    url_name="case_person",
)
def delete_person(request, case_uuid: UUID, person_uuid: UUID):
    """Deletes the provided person from the provided case. One person per case always needs to remain. So, if there are
    less than 2 persons present in the case the return status code will be 403.
    """
    case = check_case_access(request, case_uuid)
    person = get_object_or_404(Person, uuid=person_uuid, case_id=case.uuid)
    if Person.objects.filter(case=person.case).count() < 2:
        return 403, core.schema.Message(detail="cannot delete last person")
    person.delete()
    return 204, core.schema.Message(detail="accepted")


@router.put(
    "/case/{case_uuid}/property/{property_uuid}",
    response={201: schemas.RealEstatePropertyOut, 404: core.schema.Message},
    url_name="case_property_uuid",
)
def update_property(
    request,
    case_uuid: UUID,
    property_uuid: UUID,
    property_to_update: schemas.RealEstatePropertyUpdate,
):
    """Updates the provided real estate property with the provided attributes in the request body.
    Needs to be triggered in the front-end when changing any field of the real estate property in the tab "Completeness".
    Provide the key value pair of an attribute with the key of the changed field and the key of the dropdown value
    of the field or True/False for boolean fields.
    """
    case = check_case_access(request, case_uuid)
    property = doccheck_services.update_property(
        case, property_uuid, property_to_update
    )
    return 201, doccheck_services.map_property_out(property)


@router.get(
    "/case/{case_uuid}/fields",
    response={200: List[schemas.Field], 404: core.schema.Message},
    url_name="case_fields",
)
def get_fields(request, case_uuid: UUID):
    """Returns all fields present in DocCheck with their translated labels and choice options if the field represents
    a dropdown menu.
    The returned values of "entity" and "key" define the model and attribute within DocCheck, the field links to.
    The value of the field needs to be set according to this attribute.
    The returned fields are dependent on the business_case_type set on the case.
    """
    case = check_case_access(request, case_uuid)
    result = doccheck_services.map_fields_out(case)
    return 200, result
