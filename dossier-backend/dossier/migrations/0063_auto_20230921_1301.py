# Generated by Django 3.2.21 on 2023-09-21 11:01

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('dossier', '0062_auto_20230913_1358'),
    ]

    operations = [
        migrations.AddField(
            model_name='account',
            name='enable_navigation_to_dossier_list',
            field=models.BooleanField(default=True, help_text="If True then show the navigation to the 'dossier list view' on the 'dossier view'. Else remove breadcrumb and show HD logo instead."),
        ),
        migrations.AlterField(
            model_name='account',
            name='enable_area_calculator',
            field=models.BooleanField(default=True, help_text="If True show actions to calculator (detail view and 'plans tab'. Else actions are not visible."),
        ),
        migrations.AlterField(
            model_name='account',
            name='enable_button_create',
            field=models.BooleanField(default=True, help_text="If True show 'Create Dossier' button in the 'dossier list view'. Else creation of dossiers in UI is not possible."),
        ),
        migrations.AlterField(
            model_name='account',
            name='enable_button_dossier_notes',
            field=models.BooleanField(default=True, help_text="If True show the Notes button on the 'dossier view'"),
        ),
        migrations.AlterField(
            model_name='account',
            name='enable_button_dossier_settings',
            field=models.BooleanField(default=True, help_text="If True show the Settings button on the 'dossier view'. Else settings are still accessible by menu next to dossier name."),
        ),
        migrations.AlterField(
            model_name='account',
            name='enable_button_download',
            field=models.BooleanField(default=True, help_text="If True show the Download button on the 'dossier view'"),
        ),
        migrations.AlterField(
            model_name='account',
            name='enable_button_open_in_new_tab',
            field=models.BooleanField(default=True, help_text="If True the action 'Open in new tab' per semantic document is available in the document menu. Else action is not visible."),
        ),
        migrations.AlterField(
            model_name='account',
            name='enable_document_upload',
            field=models.BooleanField(default=True, help_text="If True show the upload dropzone in 'dossier view'. Else do not show upload zone and move buttons for settings, notes, download to the header line."),
        ),
        migrations.AlterField(
            model_name='account',
            name='enable_dossier_permission',
            field=models.BooleanField(default=False, help_text="If True the access delegation feature is activated for the account and users can delegate access to other users. Else users have only access to their own dossiers. Better name would be 'enable_access_delegation'."),
        ),
        migrations.AlterField(
            model_name='account',
            name='enable_download_extraction_excel',
            field=models.BooleanField(default=True, help_text="If True '000 Hypodossier Datenextraktion.xlsx' is part of the ZIP file downloadable as dossier download in the UI. Else this file is not added to ZIP."),
        ),
        migrations.AlterField(
            model_name='account',
            name='enable_rendering_photos_tab',
            field=models.BooleanField(default=True, help_text="If True show the 'photos tab' that shows all photo page objects in dossier. Else tab is not visible."),
        ),
        migrations.AlterField(
            model_name='account',
            name='enable_rendering_structure_details_tab',
            field=models.BooleanField(default=False, help_text="If True show the 'extended structure with details tab' that allows browsing the documents. Else tab is not visible."),
        ),
        migrations.AlterField(
            model_name='account',
            name='enable_rendering_structure_tab',
            field=models.BooleanField(default=True, help_text="If True show the 'simple structure tab' without document view functionality. Else tab is not visible."),
        ),
    ]
