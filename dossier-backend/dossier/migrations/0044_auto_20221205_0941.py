# Generated by Django 3.2.16 on 2022-12-05 08:41

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('dossier', '0043_account_enable_area_calculator_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='DossierRole',
            fields=[
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('key', models.CharField(max_length=255)),
                ('name_de', models.CharField(blank=True, max_length=255, null=True)),
                ('name_en', models.Char<PERSON>ield(blank=True, max_length=255, null=True)),
                ('name_fr', models.CharField(blank=True, max_length=255, null=True)),
                ('name_it', models.CharField(blank=True, max_length=255, null=True)),
                ('user_selectable', models.BooleanField(default=False)),
                ('show_separate_filter', models.BooleanField(default=False)),
            ],
        ),
        migrations.CreateModel(
            name='UserInvolvement',
            fields=[
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.RenameField(
            model_name='account',
            old_name='active_dossier_state_machine',
            new_name='active_work_status_state_machine',
        ),
        migrations.RenameField(
            model_name='dossier',
            old_name='dossier_status',
            new_name='work_status',
        ),
        migrations.AddField(
            model_name='account',
            name='dmf_endpoint',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='dossier',
            name='account',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='dossier.account'),
        ),
        migrations.AlterField(
            model_name='dossier',
            name='owner',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='dossieruser',
            name='account',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='dossier.account'),
        ),
        migrations.AlterField(
            model_name='dossieruser',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='fileexception',
            name='exception_type',
            field=models.IntegerField(choices=[(1, 'Unknown Exception'), (2, 'Not Readable'), (3, 'Password Protected'), (4, 'Unsupported Filetype'), (5, 'Too Small File'), (6, 'Xlsm File Cannot Be Converted')], default=1),
        ),
        migrations.AddConstraint(
            model_name='dossieruser',
            constraint=models.UniqueConstraint(fields=('account', 'user'), name='unique_user_per_account'),
        ),
        migrations.AddField(
            model_name='userinvolvement',
            name='dossier',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='dossier.dossier'),
        ),
        migrations.AddField(
            model_name='userinvolvement',
            name='role',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='dossier.dossierrole'),
        ),
        migrations.AddField(
            model_name='userinvolvement',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='dossier.dossieruser'),
        ),
        migrations.AddField(
            model_name='dossierrole',
            name='account',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='dossier.account'),
        ),
        migrations.AddConstraint(
            model_name='dossierrole',
            constraint=models.UniqueConstraint(fields=('account', 'key'), name='unique_dossier_roles_per_account'),
        ),
    ]
