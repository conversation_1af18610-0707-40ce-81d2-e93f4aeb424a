# Generated by Django 4.2.9 on 2024-01-15 15:29

from django.db import migrations, models
import uuid


class Migration(migrations.Migration):
    dependencies = [
        ("dossier", "0082_originalfile_force_access_mode"),
    ]

    operations = [
        migrations.CreateModel(
            name="CopyDossierHistory",
            fields=[
                (
                    "uuid",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("source_dossier_uuid", models.UUIDField()),
                (
                    "source_dossier_external_id",
                    models.Char<PERSON>ield(blank=True, max_length=255, null=True),
                ),
                ("new_dossier_uuid", models.UUIDField()),
                (
                    "new_dossier_external_id",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
    ]
