# Generated by Django 3.2.22 on 2023-10-06 07:20

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('dossier', '0068_remove_originalfile_realestate_property'),
    ]

    operations = [
        migrations.AddField(
            model_name='account',
            name='navigation_strategy',
            field=models.CharField(choices=[('DEFAULT', 'Standard navigation dossier list view / dossier view / dossier details'), ('NO_DOSSIER_LIST_BRANDED', 'Only access to dossier view, not to the dossier list view. Application is Hypodossier branded'), ('NO_DOSSIER_LIST_UNBRANDED', 'Only access to dossier view, not to the dossier list view. Application is whitelabelled (not Hypodossier branded)')], default='DEFAULT', help_text='Controls the look and feel of the application and decides if the dossier list view is accessible', max_length=40, verbose_name='Navigation strategy'),
        ),
    ]
