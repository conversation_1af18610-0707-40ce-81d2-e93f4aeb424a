# Generated by Django 3.2.23 on 2023-12-22 09:49

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('dossier', '0080_account_enable_dossier_assignment'),
    ]

    operations = [
        migrations.AddField(
            model_name='fileexception',
            name='fr',
            field=models.TextField(blank=True, default=None, null=True),
        ),
        migrations.AddField(
            model_name='fileexception',
            name='it',
            field=models.TextField(blank=True, default=None, null=True),
        ),
        migrations.AddField(
            model_name='originalfile',
            name='force_external_semantic_document_id',
            field=models.CharField(blank=True, default=None, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='fileexception',
            name='exception_type',
            field=models.IntegerField(choices=[(1, 'Unknown Exception'), (2, 'Not Readable'), (3, 'Password Protected'), (4, 'Unsupported Filetype'), (5, 'Too Small File'), (6, 'Xlsm File Cannot Be Converted'), (7, 'Ocr Filetype Processing')], default=1),
        ),
    ]
