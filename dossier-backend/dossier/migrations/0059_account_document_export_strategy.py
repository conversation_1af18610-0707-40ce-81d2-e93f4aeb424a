# Generated by Django 3.2.20 on 2023-08-30 14:55

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('dossier', '0058_jwk'),
    ]

    operations = [
        migrations.AddField(
            model_name='account',
            name='document_export_strategy',
            field=models.CharField(choices=[('SINGLEPDF', 'Merge Documents into single PDF  for entire dossier, Export single PDF'), ('MULTIPLEPDF', 'Single PDF per Semantic Document, Export multiple PDFs')], default='MULTIPLEPDF', help_text='Should documents be exported as multiple PDFs or as a single merged PDF?', max_length=12, verbose_name='Document Export strategy'),
        ),
    ]
