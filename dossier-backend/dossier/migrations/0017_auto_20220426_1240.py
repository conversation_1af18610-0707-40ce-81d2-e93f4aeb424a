# Generated by Django 3.2.13 on 2022-04-26 10:40
import logging
import structlog

from django.db import migrations, transaction


logger = structlog.get_logger()


def forwards(apps, schema_editor):
    DocumentCategory = apps.get_model("dossier", "DocumentCategory")
    SemanticPage = apps.get_model("semantic_document", "SemanticPage")
    SemanticDocument = apps.get_model("semantic_document", "SemanticDocument")

    with transaction.atomic():
        last = None
        for document_category in DocumentCategory.objects.order_by("name").all():
            if last and last.name == document_category.name:
                logger.warning(
                    f"found duplicate document category {last} {document_category}, migrating to unique document category"
                )
                for processed_page in document_category.processedpage_set.all():
                    processed_page.document_category = last
                    processed_page.save()

                for semantic_page in SemanticPage.all_objects.filter(
                    document_category=document_category
                ).all():
                    semantic_page.document_category = last
                    semantic_page.save()

                for semantic_documents in SemanticDocument.all_objects.filter(
                    document_category=document_category
                ).all():
                    semantic_documents.document_category = last
                    semantic_documents.save()
                document_category.delete()
            else:
                last = document_category


class Migration(migrations.Migration):
    dependencies = [
        ("dossier", "0016_auto_20220426_1240"),
    ]

    operations = [migrations.RunPython(forwards)]
