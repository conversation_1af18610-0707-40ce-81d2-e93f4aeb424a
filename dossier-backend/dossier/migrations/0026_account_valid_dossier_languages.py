# Generated by Django 4.1.2 on 2022-10-19 12:44

import django.contrib.postgres.fields
from django.db import migrations, models
import dossier.models


class Migration(migrations.Migration):

    dependencies = [
        ('dossier', '0025_account_max_dossier_expiry_duration_days_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='account',
            name='valid_dossier_languages',
            field=django.contrib.postgres.fields.ArrayField(base_field=models.TextField(max_length=255), default=dossier.models.get_valid_dossier_languages_default, size=None),
        ),
    ]
