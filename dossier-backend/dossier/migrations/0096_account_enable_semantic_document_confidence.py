# Generated by Django 4.2.13 on 2024-07-04 08:45

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("dossier", "0095_alter_fileexception_exception_type"),
    ]

    operations = [
        migrations.AddField(
            model_name="account",
            name="enable_semantic_document_confidence",
            field=models.BooleanField(
                default=False,
                help_text="If True show the confidence indicator (circles) for semantic documents. Else no information about confidence is shown.",
            ),
        ),
    ]
