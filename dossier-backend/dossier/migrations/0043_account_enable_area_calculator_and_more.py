# Generated by Django 4.1.2 on 2022-11-14 14:03

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('dossier', '0042_dossier_access_mode'),
    ]

    operations = [
        migrations.AddField(
            model_name='account',
            name='enable_area_calculator',
            field=models.<PERSON>olean<PERSON>ield(default=True),
        ),
        migrations.AddField(
            model_name='account',
            name='enable_debug_document',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='account',
            name='enable_dossier_search',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='account',
            name='enable_dossier_sorting',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='account',
            name='enable_download_document',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='account',
            name='enable_download_dossier',
            field=models.<PERSON><PERSON><PERSON><PERSON>ield(default=True),
        ),
        migrations.Add<PERSON>ield(
            model_name='account',
            name='enable_download_original_file_link',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='account',
            name='enable_drag_and_drop_in_page_view',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='account',
            name='enable_error_detail',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='account',
            name='enable_feedback_form',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='account',
            name='enable_hovered_section_on_page_view',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='account',
            name='enable_icons_on_page_view',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='account',
            name='enable_rendering_hurdles_tab',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='account',
            name='enable_rendering_structure_tab',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='account',
            name='enable_show_deleted_elements',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='account',
            name='enable_uploading_files',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='account',
            name='enable_zoom_feature',
            field=models.BooleanField(default=True),
        ),
    ]
