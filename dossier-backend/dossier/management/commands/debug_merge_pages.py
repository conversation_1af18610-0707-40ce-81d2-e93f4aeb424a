import structlog
import random

import djclick as click
from faker import Faker
from uuid import UUI<PERSON>

from dossier.fakes import (
    add_fake_semantic_documents_for_merge_testing,
)
from dossier.models import (
    Account,
    RealestateProperty,
    Dossier,
    DocumentCategory,
)


logger = structlog.get_logger()


@click.command()
@click.argument("dossier_uuid", type=click.UUID)
@click.argument("account_key", type=click.STRING, default="default")
def debug_merge_pages(dossier_uuid: UUID, account_key: str):
    # Debug the following issue:

    # 2 real estate properties, two docs each (total 4, type 615) with two photo documents each.
    # 2 docs, no real estate (615).
    # 6 docs total.
    # None of them had any page objects of type photo. Combined. Got an unknown document category, rather than 615

    default_account = Account.objects.get(key=account_key)

    default_account.enable_real_estate_properties = True

    default_account.save()

    faker = Faker(locale="de_CH")

    faker.seed_instance(random.random())

    dossier = Dossier.objects.get(uuid=dossier_uuid, account=default_account)

    # Delete any pre-existing semantic documents
    dossier.semantic_documents.all().delete()

    document_category_photo = DocumentCategory.objects.filter(
        account=dossier.account, name="PROPERTY_PHOTOS"
    ).first()

    real_estate_property_mansion, _ = RealestateProperty.objects.update_or_create(
        defaults=dict(
            title="MANSION",
            floor=(
                random.randint(1, 20) if random.random() > 0.8 else None
            ),  # Generating random floor number
            street=faker.street_name(),
            street_nr=faker.building_number() if random.random() > 0.8 else None,
            zipcode=faker.postcode(),
            city=faker.city(),
        ),
        dossier=dossier,
        key="MANSION",
    )

    real_estate_property_flat, _ = RealestateProperty.objects.update_or_create(
        defaults=dict(
            title="FLAT",
            floor=(
                random.randint(1, 20) if random.random() > 0.8 else None
            ),  # Generating random floor number
            street=faker.street_name(),
            street_nr=faker.building_number() if random.random() > 0.8 else None,
            zipcode=faker.postcode(),
            city=faker.city(),
        ),
        dossier=dossier,
        key="FLAT",
    )

    # Create semantic documents for each category with real estate property
    add_fake_semantic_documents_for_merge_testing(
        dossier=dossier,
        document_category=document_category_photo,
        num_docs=2,
        real_estate_property=real_estate_property_mansion,
    )
    add_fake_semantic_documents_for_merge_testing(
        dossier=dossier,
        document_category=document_category_photo,
        num_docs=2,
        real_estate_property=real_estate_property_flat,
    )
    add_fake_semantic_documents_for_merge_testing(
        dossier=dossier,
        document_category=document_category_photo,
        num_docs=2,
    )
