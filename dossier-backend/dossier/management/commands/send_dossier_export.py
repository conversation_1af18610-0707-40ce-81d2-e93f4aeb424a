import os
import structlog
from dossier.admin import DossierResourceForExport, OriginalFileResourceForExport
from pathlib import Path
from tempfile import TemporaryDirectory

import djclick as click
from django.conf import settings

from datetime import datetime

import smtplib
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.mime.base import MIMEBase
from email import encoders

import schedule
import time

logger = structlog.get_logger()


@click.command()
@click.argument("account_uuid")
@click.argument("emails")
@click.argument("send_time")
@click.argument("day")
def send_dossier_export(account_uuid, emails, send_time, day):

    def send_email():
        resource = DossierResourceForExport()
        resource.account_uuid = account_uuid
        dataset = resource.export()
        filename = "dossier_export.xlsx"

        resource_original_files = OriginalFileResourceForExport()
        resource_original_files.account_uuid = account_uuid
        dataset_original_files = resource_original_files.export()
        filename_original_files = "files_export.xlsx"
        with TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            filepath = os.path.join(temp_path, filename)
            with open(filepath, "wb") as f:
                f.write(dataset.xlsx)

            filepath_original_files = os.path.join(temp_path, filename_original_files)
            with open(filepath_original_files, "wb") as f:
                f.write(dataset_original_files.xlsx)

            # Set your credentials and email details
            username = "<EMAIL>"
            password = settings.AUTH_EMAIL_APP_PASSWORD

            sender_email = "<EMAIL>"
            receiver_emails = [
                email.strip() for email in emails.split(",")
            ]  # Convert the comma-separated string into a list of email addresses
            envelope_from = "<EMAIL>"  # The envelope sender (used in SMTP 'MAIL FROM')
            reply_to = "<EMAIL>"  # The reply-to address (where replies will be sent)
            subject = "Dossier Export from HypoDossier"
            body = "Hello,\n\nPlease find the latest Dossier Export in the attachment!\n\nBest,\n\nTeam HypoDossier"

            # Create a multipart message
            message = MIMEMultipart()
            message["From"] = sender_email
            message["To"] = ", ".join(receiver_emails)
            message["Subject"] = subject
            message["Reply-To"] = reply_to
            message.attach(MIMEText(body, "plain"))

            # Connect to the Office365 SMTP server and send the email
            smtp_server = "smtp.office365.com"
            port = 587  # TLS port

            def attach_file(filepath, filename, message):
                try:
                    # Add current date (e.g. 2025-03-28) to the filename
                    date_str = datetime.now().strftime("%Y-%m-%d")
                    name_parts = filename.rsplit(".", 1)
                    if len(name_parts) == 2:
                        filename_with_date = (
                            f"{name_parts[0]}_{date_str}.{name_parts[1]}"
                        )
                    else:
                        filename_with_date = f"{filename}_{date_str}"

                    with open(filepath, "rb") as attachment:
                        # Create a MIMEBase object and read the attachment
                        part = MIMEBase("application", "octet-stream")
                        part.set_payload(attachment.read())

                    # Encode the file in base64 and add header for the attachment
                    encoders.encode_base64(part)
                    part.add_header(
                        "Content-Disposition",
                        f"attachment; filename={filename_with_date}",
                    )
                    message.attach(part)
                except Exception as e:
                    print(f"Could not attach file '{filename}':", e)

            # Attach files using the helper function
            attach_file(filepath, filename, message)
            attach_file(filepath_original_files, filename_original_files, message)

            try:
                # Connect to the server and start TLS for security
                server = smtplib.SMTP(smtp_server, port)
                server.starttls()  # Upgrade the connection to secure
                server.login(username, password)

                # Note: envelope_from is passed as the sender in sendmail()
                server.sendmail(envelope_from, receiver_emails, message.as_string())
                print("Email sent successfully!")
            except Exception as e:
                print("Error sending email:", e)
            finally:
                server.quit()
            logger.info(f"Export completed and sent {filename}")

    day = day.lower()
    valid_days = {
        "monday",
        "tuesday",
        "wednesday",
        "thursday",
        "friday",
        "saturday",
        "sunday",
    }
    if day not in valid_days:
        print(
            "Invalid day parameter provided. Use one of: monday, tuesday, wednesday, thursday, friday, saturday, sunday."
        )
        return

    # Validate send_time format (HH:MM)
    try:
        datetime.strptime(send_time, "%H:%M")
    except ValueError:
        print(
            "Invalid send_time format. Please use HH:MM in 24-hour format (e.g., 07:00)."
        )
        return

    print(f"Scheduling email for every {day.capitalize()} at {send_time}")

    # Dynamically schedule the job using the provided day and time.
    schedule_func = getattr(schedule.every(), day)
    schedule_func.at(send_time).do(send_email)

    print("Scheduler started. Waiting for the next scheduled run...")

    # Keep the script running to allow the scheduler to run indefinitely.
    while True:
        schedule.run_pending()
        time.sleep(60)  # Check every minute
