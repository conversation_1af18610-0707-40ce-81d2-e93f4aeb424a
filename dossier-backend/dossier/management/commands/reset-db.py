import subprocess

import dj<PERSON>lick as click
import psycopg2
from django.conf import settings
from django.db import connection
from django.utils.timezone import now
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT

from assets import ASSETS_PATH
from dossier.management.utils import load_default_accounts


def run_sql(sql):
    username = settings.DATABASES["default"]["USER"]
    host = settings.DATABASES["default"]["HOST"]
    password = settings.DATABASES["default"]["PASSWORD"]
    port = settings.DATABASES["default"]["PORT"]
    conn = psycopg2.connect(
        database="postgres", user=username, password=password, host=host, port=port
    )
    conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
    cur = conn.cursor()
    cur.execute(sql)
    conn.close()


@click.command()
def reset():
    start = now()
    from django.core.management import call_command

    database_name = settings.DATABASES["default"]["NAME"]

    run_sql(
        "SELECT pg_terminate_backend(pg_stat_activity.pid)\n"
        "FROM pg_stat_activity\n"
        f"WHERE pg_stat_activity.datname = '{database_name}' \n"
        "  AND pid <> pg_backend_pid();\n"
        "\n"
    )
    run_sql("DROP DATABASE IF EXISTS %s" % database_name)
    run_sql(f"CREATE DATABASE {database_name} WITH TEMPLATE template1")

    username = settings.DATABASES["default"]["USER"]
    host = settings.DATABASES["default"]["HOST"]
    password = settings.DATABASES["default"]["PASSWORD"]

    db_latest = ASSETS_PATH / "db_latest.tar"
    cmd = f"{settings.POSTGRES_PATH}pg_restore -h {host} -U {username} -d {database_name} --no-owner {db_latest}"
    print("command", cmd)

    res = subprocess.run(
        cmd, shell=True, env={"PGPASSWORD": password}, capture_output=True
    )
    print(res.returncode, res.stdout, res.stderr)
    res.check_returncode()

    settings.DATABASES[connection.alias]["NAME"] = database_name
    connection.settings_dict["NAME"] = database_name
    call_command("migrate", "--noinput")
    call_command("set_default_jwks")
    call_command("configure_default_field_set")
    end = now()
    print("duration", end - start)

    # Creating and loading a new db dump no longer works due to some sort of FK constraint issue
    # Hence create accounts needed for locally running the app here

    load_default_accounts()
