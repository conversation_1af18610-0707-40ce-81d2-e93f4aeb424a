from statistics import mean
from django.utils import timezone

import djclick as click
import structlog

from dossier.api import create_semantic_dossier_simple
from dossier.models import Dossier
from dossier.schemas import SemanticDossierSimple

logger = structlog.get_logger()


@click.command()
@click.argument(
    "dossier_uuid", type=click.STRING, default="254e93ec-c0f2-4133-be04-24170c60e650"
)
def performance_datav2(dossier_uuid: str):
    dossier_uuid = "3c10547c-e33a-45be-a750-6d13e875df87"

    logger.info("performance datav2 start...", dossier_uuid=dossier_uuid)
    durations = []
    num_runs = 5
    for i in range(1, num_runs):
        start = timezone.now()
        dossier = Dossier.objects.get(uuid=dossier_uuid)
        assert dossier

        show_soft_deleted = False
        sds: SemanticDossierSimple = create_semantic_dossier_simple(
            dossier, show_soft_deleted
        )

        duration = (timezone.now() - start).total_seconds()
        durations.append(duration)
        logger.info(
            "duration",
            duration=duration,
            num_original_files=sds.count_original_files,
            # these are incorrectly named in API, sds.extracted_files is actually a dict with
            # number of original files entries
            num_original_files_from_list=len(sds.extracted_files),
            num_extracted_files_v2=len(sds.extracted_files_v2),
            num_sem_docs=len(sds.semantic_documents),
        )

    logger.info("average duration", average_seconds=mean(durations), num_runs=num_runs)
