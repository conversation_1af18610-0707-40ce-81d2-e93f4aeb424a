import djclick as click

from dossier.services import add_case_to_dossiers_with_doccheck


@click.group()
def cli():
    pass


@cli.command()
@click.argument("account_key")
def add_case_to_existing_dossiers(account_key: str):
    """
    For all accounts that have an active doccheck selected in the account:
    Create a doccheck case in every dossier that does not yet have one.
    
     python manage.py doccheck_dossier add-case-to-existing-dossiers default
    
    @return: 
    """ ""
    add_case_to_dossiers_with_doccheck(account_key)
