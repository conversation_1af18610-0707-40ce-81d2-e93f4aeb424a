import structlog

import djclick as click

from dossier.dossier_access_external import install_dossier_access_check_providers

logger = structlog.get_logger()


@click.group()
def grp():
    pass


@grp.command()
def install():
    """
    Add all missing access check providers to the db.

    python manage.py dossier_access_check install

    @return:
    """
    install_dossier_access_check_providers()
