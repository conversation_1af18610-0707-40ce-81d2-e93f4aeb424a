from datetime import timed<PERSON><PERSON>, datetime
from typing import Tuple

import structlog
from django.contrib.auth.models import User
from django.utils import timezone
from ninja.errors import AuthenticationError, HttpError

from dossier import jwt_extract
from dossier.models import (
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON>r<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON>r<PERSON>ccessG<PERSON>,
    <PERSON><PERSON>,
)
from projectconfig.authentication import get_user_or_create

logger = structlog.get_logger(__name__)

DAP_DEFAULT_CHECK_ALWAYS_TRUE_NAME = "default check always true"
DAP_DEFAULT_CHECK_ALWAYS_FALSE_NAME = "default check always false"
DAP_DEFAULT_ACCESS_GRANT_CHECK = "default access grant check"


def install_dossier_access_check_providers():
    """
    Populate database and add dossier access providers that do not yet exist.
    Do nothing if an access provider already exists.
    @return:
    """
    all_providers = [
        "zkb external check (call zkb)",
        "zkb external check always true",
        "zkb external check always false",
        DAP_DEFAULT_CHECK_ALWAYS_TRUE_NAME,
        DAP_DEFAULT_CHECK_ALWAYS_FALSE_NAME,
        DAP_DEFAULT_ACCESS_GRANT_CHECK,
    ]

    # Note: "external check always true" and "external check always false" are legacy terms and
    # should not be used anymore (replaced by "zkb external check always true" and
    # "zkb external check always falses"

    created_providers = []
    for pname in all_providers:
        p, created = DossierAccessCheckProvider.objects.get_or_create(name=pname)
        if created:
            created_providers.append(p.name)

    logger.info(
        "initialize dossier access providers", created_providers=created_providers
    )


def initialize_default_dossier_access_check_providers():
    from dossier.models import account_specific_access_checks

    account_specific_access_checks[DAP_DEFAULT_CHECK_ALWAYS_TRUE_NAME] = (
        dap_default_dossier_access_always_true
    )
    account_specific_access_checks[DAP_DEFAULT_CHECK_ALWAYS_FALSE_NAME] = (
        dap_default_dossier_access_always_false
    )
    account_specific_access_checks[DAP_DEFAULT_ACCESS_GRANT_CHECK] = (
        dap_default_access_grant_check
    )


def dap_default_dossier_access_always_true(jwt: dict, dossier: Dossier) -> None:
    """
    Make no checks, always grant access

    Raises:
        ninja.exceptions.AuthenticationError: If no access shall be granted.
    """
    return None  # this is ok


def dap_default_dossier_access_always_false(jwt: dict, dossier: Dossier) -> None:
    """
    Make no checks, never grant access

    Raises:
        ninja.exceptions.AuthenticationError: If no access shall be granted.
    """
    raise AuthenticationError()


def dap_default_access_grant_check(jwt: dict, dossier: Dossier) -> None:
    """
    Grant access if corresponding entry exists in DossierAccessGrant model


    Raises:
        ninja.exceptions.AuthenticationError: If no access shall be granted.
    """
    account = jwt_extract.get_account(jwt)
    if dossier.account != account:
        raise HttpError(
            404,
            f"Dossier with uuid {dossier.uuid} and external_id {dossier.external_id} not found in account{account.key}",
        )

    if jwt_extract.is_manager(jwt):
        # This is a dossier manager -> always grant access
        return None

    username = jwt.get("preferred_username")
    if not username:
        raise HttpError(
            404,
            "No 'preferred_username' found in token",
        )

    dossier_user = get_user_or_create(
        account=dossier.account,
        # user_id is filled with the UserID which is not the username.
        # the username is the preferred_username
        username=username,
        email=jwt.get("email"),
        fname=jwt.get("given_name"),
        lname=jwt.get("family_name"),
    )

    access_grant = DossierAccessGrant.objects.filter(
        dossier=dossier, user=dossier_user.user, expires_at__gt=timezone.now()
    ).first()

    if access_grant and access_grant.has_access:
        return None  # grant access

    raise AuthenticationError()


def create_or_update_access_grant(
    user: User,
    dossier: Dossier,
    expires_at: datetime = timezone.now() + timedelta(minutes=9),
    has_access: bool = True,
    scope: Scope = Scope.READ_WRITE,
) -> Tuple[DossierAccessGrant, bool]:
    """
    Use this method to create or update an access grant. This ensures that for each (user, dossier) there is
    only one grant
    @param user:
    @param dossier:
    @param expires_at:
    @param has_access:
    @return:
    """
    grant, created = DossierAccessGrant.objects.update_or_create(
        user=user,
        dossier=dossier,
        defaults={
            "expires_at": expires_at,
            "has_access": has_access,
            "scope": scope,
        },
    )
    return grant, created


def get_access_check_provider_default_dossier_access_grant():
    dap, _ = DossierAccessCheckProvider.objects.get_or_create(
        name=DAP_DEFAULT_ACCESS_GRANT_CHECK
    )
    return dap
