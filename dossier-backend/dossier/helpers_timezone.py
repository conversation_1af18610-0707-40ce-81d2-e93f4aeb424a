from datetime import datetime as datetime_datetime, timedelta
from django.utils import timezone as django_timezone
from datetime import timezone as datetime_timezone


def create_faker_past_datetime_with_timezone(
    faker, days_in_the_past: int = 30
) -> datetime_datetime:
    """
    All datetimes that we use must be timezone aware. For a random datetime in the past it is
    not important what timezone it is so we use UTC.

    past_datetime is not supported anymore

    Note:
    :param faker:
    :param days_in_the_past: Number of days in the past to generate a random date (positive integer)
    :return:
    """
    end_date = datetime_datetime.now(datetime_timezone.utc)
    start_date = end_date - timedelta(days=days_in_the_past)
    dt = faker.date_time_between(
        start_date=start_date, end_date=end_date, tzinfo=datetime_timezone.utc
    )
    return dt


def log_timezone_info(logger):
    utc_now = django_timezone.now()  # Aware datetime in UTC
    logger.info(
        "Timezone check",
        django_now_zurich_local_time=django_timezone.localtime(utc_now).strftime(
            "%Y-%m-%d %H:%M:%S %Z%z"
        ),
        django_now_timezone_aware=django_timezone.now().strftime(
            "%Y-%m-%d %H:%M:%S %Z%z"
        ),
        datetime_now_naive_no_timezone=datetime_datetime.now().strftime(
            "%Y-%m-%d %H:%M:%S"
        ),
    )


def create_local_datetime() -> datetime_datetime:
    utc_now = django_timezone.now()  # Aware datetime in UTC
    zurich_now = django_timezone.localtime(utc_now)  # Convert to Zurich time
    return zurich_now
