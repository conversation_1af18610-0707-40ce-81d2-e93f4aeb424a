from dataclasses import dataclass, field as dataclass_field
from typing import Type, Callable, List, Any, Union
from uuid import uuid4, UUID

import minio
import structlog
from django.conf import settings
from django.db import models, transaction
from django.db.models import Exists, OuterRef, Q
from django.utils import timezone
from minio.commonconfig import CopySource
from minio.helpers import ObjectWriteResult
from structlog.contextvars import bound_contextvars

from core.generics import T_Model
from doccheck.services import create_case
from dossier.models import (
    Account,
    Dossier,
    DossierFile,
    DocumentCategory,
    OriginalFile,
    ExtractedFile,
    UserInvolvement,
    RealestateProperty,
    AssignedRealestatePropertyOriginalFile,
    CopyDossierHistory,
    FileException,
    FileStatus,
)
from dossier.services import delete_dossier_associated_objects
from dossier.services_event_consumer import create_dossier_file_from_url
from processed_file.models import ProcessedPage, ProcessedFile, PageObject
from semantic_document.models import (
    SemanticDocument,
    SemanticPage,
    SemanticDocumentPageObject,
    AssignedRealEstatePropertySemanticDocument,
    SemanticPagePageObject,
)

from collections import OrderedDict

logger = structlog.get_logger(__name__)


FieldCopier = Callable[[T_Model, T_Model, str], None]


def copy_single_dossier(dossier: Dossier, external_id: str = None) -> Dossier:
    new_dossier = Dossier()
    fields = [
        field for field in Dossier._meta.get_fields() if isinstance(field, models.Field)
    ]
    for field in fields:
        if field.primary_key:
            setattr(new_dossier, field.name, uuid4())
        else:
            setattr(new_dossier, field.name, getattr(dossier, field.name))
    if external_id:
        new_dossier.external_id = external_id
    new_dossier.save()
    return new_dossier


@dataclass
class ModelCopier:
    model: Type[models.Model]
    primary_key: str
    field_copiers: dict[str, FieldCopier] = dataclass_field(default_factory=dict)


def copy_dossier_models(
    dossier: Dossier,
    external_id: str = None,
    target_dossier: Dossier = None,
    new_dossier_account: Account = None,
) -> OrderedDict[UUID, Union[Type[T_Model], T_Model]]:
    """
    Copy a dossier and all its associated related models, optionally into an existing dossier.

    This function creates a deep copy of a dossier, including all related models such as
    UserInvolvement, RealestateProperty, DossierFile, OriginalFile, etc. It can either create
    a new dossier or copy the data into an existing target dossier.

    Args:
        dossier (Dossier): The source dossier to be copied.
        external_id (str, optional): An external ID to be assigned to the new dossier.
                                     If not provided, the new dossier will not have an external ID.
        target_dossier (Dossier, optional): An existing dossier to copy the data into.
                                            If not provided, a new dossier will be created.

    Returns:
        OrderedDict[UUID, Union[Type[T_Model], T_Model]]: An ordered dictionary containing the newly
        created or updated model instances, keyed by the UUID of the original instances.

    Raises:
        Exception: If any error occurs during the copying process. The error is logged, and any
                   partially created S3 objects are attempted to be deleted.

    Notes:
        - This function uses a database transaction to ensure atomicity.
        - When copying into an existing dossier (target_dossier is provided), it will update
          existing related models or create new ones as needed.
        - S3 objects associated with DossierFiles are also copied.
        - A CopyDossierHistory entry is created to log the copy operation.
        - In case of an error, the function attempts to clean up any created S3 objects and
          logs the error details.

    Example:
        # To create a new copy of a dossier
        new_instances = copy_dossier_models(existing_dossier, external_id="NEW123")

        # To copy into an existing dossier
        updated_instances = copy_dossier_models(source_dossier, target_dossier=existing_dossier)
    """

    old_dossier_uuid = dossier.uuid
    new_instances: OrderedDict[UUID, T_Model] = OrderedDict()

    start_time = timezone.now()

    # For structlog, bound_contextvars means any loggers inside the context
    # contain dossier_uuid
    with bound_contextvars(
        old_dossier_uuid=str(old_dossier_uuid),
        old_dossier_external_id=dossier.external_id,
        account_key=dossier.account.key,
    ):
        try:
            # We want things to happen inside a transaction, so that we are not left with partially created dossiers
            # in the UI.
            # A failed job might leave orphan documents in S3 - however, this is the preferred alternative, as we can run a
            # garbage collection job to remove these at a later time.
            # Not as files are stored in a hierarchical manner, we would just need to find the dossier UUID, an remove the parent
            # folder (namespace), to clean up everything underneath/associated
            with transaction.atomic():
                # List of foreign keys to update via lookup in new_instances when creating new model instances
                update_FK = [
                    Dossier,
                    OriginalFile,
                    DossierFile,
                    ProcessedFile,
                    SemanticDocument,
                    SemanticPage,
                    PageObject,
                    ProcessedPage,
                    ExtractedFile,
                    RealestateProperty,
                    SemanticPagePageObject,
                ]

                def one_to_one_copy(
                    old_instance: models.Model,
                    new_instance: models.Model,
                    field_name: str,
                ):
                    setattr(new_instance, field_name, getattr(old_instance, field_name))

                def lookup_foreign_key(
                    old_instance: models.Model,
                    new_instance: models.Model,
                    field_name: str,
                ):

                    old_value = getattr(old_instance, field_name)
                    if old_value is None:
                        setattr(new_instance, field_name, None)
                    else:
                        new_value = new_instances[old_value.pk]
                        setattr(new_instance, field_name, new_value)

                def set_document_category(
                    old_instance: models.Model,
                    new_instance: models.Model,
                    field_name: str,
                ):
                    if new_dossier_account is not None:
                        dcid = getattr(old_instance, field_name).id
                        dc = DocumentCategory.objects.get(
                            account=new_dossier_account, id=dcid
                        )
                        setattr(new_instance, field_name, dc)

                def new_uuid(
                    old_instance: models.Model,
                    new_instance: models.Model,
                    field_name: str,
                ):
                    setattr(new_instance, field_name, uuid4())

                def set_null(
                    old_instance: models.Model,
                    new_instance: models.Model,
                    field_name: str,
                ):
                    setattr(new_instance, field_name, None)

                def _create_dossier_file_from_url(
                    old_instance: DossierFile,
                    new_instance: DossierFile,
                    field_name: str,
                ):
                    dossier_file = create_dossier_file_from_url(
                        new_instance.dossier.uuid,
                        new_instance.bucket,
                        old_instance.get_fast_url(),
                    )

                    new_instance.data = dossier_file.data

                def _create_case(
                    old_instance: Dossier, new_instance: Dossier, field_name: str
                ):
                    if old_instance.doccheck_case:
                        new_instance.doccheck_case = create_case(
                            doc_check=old_instance.doccheck_case.doc_check,
                            business_case_type=old_instance.doccheck_case.business_case_type,
                        )

                def get_default_copier(
                    field: models.Field,
                ) -> Callable[[Any, Any, Any], None]:
                    if isinstance(field, models.ForeignKey):
                        if field.related_model in update_FK:
                            return lookup_foreign_key

                    if field.primary_key and isinstance(field, models.UUIDField):
                        return new_uuid

                    return one_to_one_copy

                models_to_copy = []

                # If target_dossier is provided, skip cloning Dossier model
                if not target_dossier:
                    models_to_copy.append(
                        ModelCopier(Dossier, "uuid", {"doccheck_case": _create_case})
                    )

                semantic_document_fields = {
                    "uuid": new_uuid,
                    "dossier": lookup_foreign_key,
                    "external_semantic_document_id": set_null,
                }

                if new_dossier_account is not None:
                    semantic_document_fields["document_category"] = (
                        set_document_category
                    )

                models_to_copy.extend(
                    [
                        ModelCopier(UserInvolvement, "dossier__uuid"),
                        ModelCopier(
                            RealestateProperty,
                            "dossier__uuid",
                        ),
                        # BEKB handling of specific fields not implemented
                        # ModelCopier(
                        #     BEKBDossierProperties,
                        #     "dossier__uuid",
                        # ),
                        ModelCopier(
                            DossierFile,
                            "dossier__uuid",
                            {"data": _create_dossier_file_from_url},
                        ),
                        ModelCopier(
                            OriginalFile,
                            "dossier__uuid",
                        ),
                        ModelCopier(
                            AssignedRealestatePropertyOriginalFile,
                            "originalfile__dossier__uuid",
                        ),
                        ModelCopier(
                            ExtractedFile,
                            "dossier__uuid",
                        ),
                        ModelCopier(
                            FileException,
                            "dossier__uuid",
                        ),
                        ModelCopier(
                            ProcessedFile,
                            "dossier__uuid",
                        ),
                        ModelCopier(ProcessedPage, "dossier__uuid"),
                        ModelCopier(
                            SemanticDocument,
                            "dossier__uuid",
                            semantic_document_fields,
                        ),
                        ModelCopier(
                            SemanticPage,
                            "dossier__uuid",
                        ),
                        ModelCopier(
                            PageObject,
                            "processed_page__dossier__uuid",
                        ),
                        ModelCopier(
                            SemanticPagePageObject,
                            "semantic_page__dossier__uuid",
                        ),
                        ModelCopier(
                            SemanticDocumentPageObject,
                            "semantic_document__dossier__uuid",
                        ),
                        ModelCopier(
                            AssignedRealEstatePropertySemanticDocument,
                            "semantic_document__dossier__uuid",
                        ),
                    ]
                )

                # If target_dossier is provided, use it instead of creating a new one
                if target_dossier:
                    new_instances[dossier.pk] = target_dossier

                # For each model in the list
                for copy_model in models_to_copy:
                    # Search for all models with FK dossier__uuid == old_dossier_uuid

                    # _base_manager
                    instances = copy_model.model._base_manager.filter(
                        **{copy_model.primary_key: old_dossier_uuid}
                    ).all()

                    # DossierFiles are wrappers around S3 buckets
                    # in some cases, such as data pipelines, the DossierFiles are created first
                    # then a rabbitmq job is dispatched to do the actual copying.
                    # There is a chance that is copy fails and we end up with orphaned DossierFiles
                    # referencing non existent files in s3
                    # we keep track of data processing status via done timestamp on DossierExport
                    # Hence filter out any DossierFiles that are referenced by DossierExport and have
                    # done == null
                    if copy_model.model == DossierFile:
                        # referenced by original_file or extracted_file or processed_file in the same dossier
                        #         python manage.py copy_dossier 254e93ec-c0f2-4133-be04-24170c60e650 254e93ec-c0f2-4133-be04-24170c60e65z
                        # expected number of instances total is 893
                        instances = instances.filter(
                            Exists(OriginalFile.objects.filter(file=OuterRef("pk")))
                            | Exists(
                                OriginalFile.objects.filter(
                                    file_replacement=OuterRef("pk")
                                )
                            )
                            | Exists(ExtractedFile.objects.filter(file=OuterRef("pk")))
                            | Exists(
                                ProcessedFile.objects.filter(
                                    extracted_file__file=OuterRef("pk")
                                )
                            )
                            | Exists(
                                ProcessedPage.objects.filter(
                                    Q(image=OuterRef("pk"))
                                    | Q(searchable_pdf=OuterRef("pk"))
                                    | Q(searchable_txt=OuterRef("pk"))
                                )
                            )
                        )
                        # ).exclude(
                        #     Exists(DossierExport.objects.filter(file=OuterRef("pk")))
                        # )

                    bulk_save_instances = []
                    # For each of those model instances
                    for instance in instances:
                        # Get all their fields
                        fields: List[models] = copy_model.model._meta.get_fields()

                        # Create a new instance
                        new_instance = copy_model.model()

                        for model_field in fields:
                            if isinstance(model_field, models.Field):
                                # Interpreter pattern
                                #  If the field's name is in the field_copiers dictionary,
                                #  it uses the corresponding function (which can be lookup_foreign_key).
                                #  If not, it uses a default function one_to_one_copy
                                #  The magic is:
                                # .get('key', default), where default is one_to_one_copy (via get_default_copier(field))
                                custom_field_copier: FieldCopier = (
                                    copy_model.field_copiers.get(
                                        model_field.name,
                                        get_default_copier(model_field),
                                    )
                                )

                                custom_field_copier(
                                    instance, new_instance, model_field.name
                                )

                        new_instances[instance.pk] = new_instance
                        bulk_save_instances.append(new_instance)

                        # Custom overwrites for specific Models - there's likely a nicer way to do this
                        if isinstance(instance, Dossier):
                            new_instance.external_id = external_id
                            if new_dossier_account:
                                new_instance.account = new_dossier_account
                            if instance.doccheck_case:
                                new_instance.doccheck_case = create_case(
                                    doc_check=instance.doccheck_case.doc_check,
                                    business_case_type=instance.doccheck_case.business_case_type,
                                )

                        if settings.TEST:
                            # This is very noisy - are you sure you want it in prod?
                            logger.debug(
                                "new instance",
                                model=copy_model.model.__name__,
                                new_instance=new_instance.pk,
                                old_instance=instance.uuid,
                            )

                    copy_model.model._base_manager.bulk_create(bulk_save_instances)

                new_dossier = next(iter(new_instances.values()))

                CopyDossierHistory.objects.create(
                    account=dossier.account,
                    source_dossier_uuid=old_dossier_uuid,
                    source_dossier_external_id=dossier.external_id,
                    new_dossier_uuid=new_dossier.uuid,
                    new_dossier_external_id=new_dossier.external_id,
                    status=FileStatus.PROCESSED,
                    duration=timezone.now() - start_time,
                )

        except Exception as e:
            if len(new_instances) > 0:
                new_dossier = next(iter(new_instances.values()))

                CopyDossierHistory.objects.create(
                    account=dossier.account,
                    source_dossier_uuid=old_dossier_uuid,
                    source_dossier_external_id=dossier.external_id,
                    new_dossier_uuid=new_dossier.uuid,
                    new_dossier_external_id=new_dossier.external_id,
                    status=FileStatus.ERROR,
                    duration=timezone.now() - start_time,
                )
                logger.error(
                    "Copy failed",
                    new_dossier_uuid=new_dossier.uuid,
                    new_dossier_external_id=new_dossier.external_id,
                    duration_sec=int((timezone.now() - start_time).total_seconds()),
                    error=str(e),
                )

                # Attempt to remove any S3 objects that may have been created
                delete_dossier_associated_objects(dossier=new_dossier)
            else:
                logger.error(
                    "Copy failed",
                    duration_sec=int((timezone.now() - start_time).total_seconds()),
                    error=str(e),
                )

            # Re-raise error, to propagate up chain
            raise e

    return new_instances


def copy_s3_bucket_objects(
    source_bucket: str, target_bucket: str, prefix: str = None
) -> List[ObjectWriteResult]:
    # Copy all objects from one bucket to another
    minio_client = minio.Minio(
        settings.S3_ENDPOINT,
        settings.S3_ACCESS_KEY,
        settings.S3_SECRET_KEY,
        secure=settings.S3_SECURE,
        region=settings.S3_REGION,
    )

    # List all objects from source_bucket
    objects = list(
        minio_client.list_objects(
            bucket_name=source_bucket, recursive=True, prefix=prefix
        )
    )

    copied_objects = []

    for obj in objects:
        object_name = obj.object_name

        # Copy object from source_bucket to target_bucket
        copied_objects.append(
            minio_client.copy_object(
                target_bucket, object_name, CopySource(source_bucket, object_name)
            )
        )

    return copied_objects


def update_dossier_to_use_different_bucket(
    dossier: Dossier,
    temp_minio_bucket: str,
) -> List[ObjectWriteResult]:

    copied_objects = copy_s3_bucket_objects(
        source_bucket=dossier.bucket,
        target_bucket=temp_minio_bucket,
        # Add the dossier uuid as a prefix so that we only copy files associated with the dossier
        # significantly speeds up tests
        prefix=str(dossier.uuid),
    )
    dossier.bucket = temp_minio_bucket
    DossierFile.objects.filter(dossier=dossier).update(bucket=temp_minio_bucket)

    dossier.save()

    return copied_objects
