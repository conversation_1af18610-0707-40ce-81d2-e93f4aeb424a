import pytest
from django.contrib.auth import get_user_model
from django.contrib.auth.models import AbstractUser
from django.core.exceptions import ValidationError
from django.utils import timezone

from dossier.models import DossierAccessGrant

User: AbstractUser = get_user_model()


pytestmark = pytest.mark.django_db


def test_unique_constraint(synthetic_dossier, test_user_1):
    DossierAccessGrant.objects.create(
        user=test_user_1,
        dossier=synthetic_dossier,
        expires_at=timezone.now() + timezone.timedelta(days=1),
        has_access=True,
    )
    with pytest.raises(ValidationError):
        DossierAccessGrant.objects.create(
            user=test_user_1,
            dossier=synthetic_dossier,
            expires_at=timezone.now() + timezone.timedelta(days=2),
            has_access=False,
        )
