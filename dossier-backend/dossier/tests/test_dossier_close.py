import pytest
from django.utils import timezone

from dossier.models import DossierCloseStrategy
from dossier.services_external import DossierCloseReadyStats, evaluate_dossier_close


@pytest.mark.parametrize(
    "num_documents_all,num_documents_export_not_started,num_documents_in_export,num_documents_exported,num_documents_unknown,allow_unknown_documents,strategy,expected_read_dossier_close",
    [
        # Step1: with unknown allowed (that means we can export them during close)
        # If some exports are ongoing with unknown allowed
        (7, 1, 2, 4, 1, True, DossierCloseStrategy.DEFAULT, True),
        (
            7,
            1,
            2,
            4,
            1,
            True,
            DossierCloseStrategy.EXPORT_SEMANTIC_DOCUMENTS_IN_DOSSIER_CLOSE,
            True,
        ),
        (
            7,
            1,
            2,
            4,
            1,
            True,
            DossierCloseStrategy.REQUIRE_SEMANTIC_DOCUMENT_EXPORT_DONE,
            False,
        ),  # exports still running
        # If no ongoing but some not started with unknown allowed
        (7, 2, 0, 3, 1, True, Do<PERSON><PERSON>CloseStrategy.DEFAULT, True),
        (
            7,
            2,
            0,
            3,
            1,
            True,
            DossierCloseStrategy.EXPORT_SEMANTIC_DOCUMENTS_IN_DOSSIER_CLOSE,
            True,
        ),
        (
            7,
            2,
            0,
            3,
            1,
            True,
            DossierCloseStrategy.REQUIRE_SEMANTIC_DOCUMENT_EXPORT_DONE,
            False,
        ),  # some not exported
        # If all exported with unknown allowed
        (7, 0, 0, 7, 1, True, DossierCloseStrategy.DEFAULT, True),
        (
            7,
            0,
            0,
            7,
            1,
            True,
            DossierCloseStrategy.EXPORT_SEMANTIC_DOCUMENTS_IN_DOSSIER_CLOSE,
            True,
        ),
        (
            7,
            0,
            0,
            7,
            1,
            True,
            DossierCloseStrategy.REQUIRE_SEMANTIC_DOCUMENT_EXPORT_DONE,
            True,
        ),
        # Step2: with unknown NOT allowed (that means we can NOT export them during close)
        (7, 1, 2, 4, 1, False, DossierCloseStrategy.DEFAULT, True),
        # because unknown cannot be exported
        (
            7,
            1,
            2,
            4,
            1,
            False,
            DossierCloseStrategy.EXPORT_SEMANTIC_DOCUMENTS_IN_DOSSIER_CLOSE,
            False,
        ),
        # exports still running and unknown not allowed
        (
            7,
            1,
            2,
            4,
            1,
            False,
            DossierCloseStrategy.REQUIRE_SEMANTIC_DOCUMENT_EXPORT_DONE,
            False,
        ),
        # If no ongoing but some not started with unknown NOT allowed
        (7, 2, 0, 3, 1, False, DossierCloseStrategy.DEFAULT, True),
        (
            7,
            2,
            0,
            3,
            1,
            False,
            DossierCloseStrategy.EXPORT_SEMANTIC_DOCUMENTS_IN_DOSSIER_CLOSE,
            False,
        ),
        # some not exported and 1 unknown not started
        (
            7,
            2,
            0,
            3,
            1,
            False,
            DossierCloseStrategy.REQUIRE_SEMANTIC_DOCUMENT_EXPORT_DONE,
            False,
        ),
        # If all exported with unknown NOT allowed
        # Inconsistent state because all exported but unknown must not be exported but we check if all exports
        # are done and then return success
        (7, 0, 0, 7, 1, False, DossierCloseStrategy.DEFAULT, True),
        (
            7,
            0,
            0,
            7,
            1,
            False,
            DossierCloseStrategy.EXPORT_SEMANTIC_DOCUMENTS_IN_DOSSIER_CLOSE,
            True,
        ),
        (
            7,
            0,
            0,
            7,
            1,
            False,
            DossierCloseStrategy.REQUIRE_SEMANTIC_DOCUMENT_EXPORT_DONE,
            True,
        ),
    ],
)
def test_stats(
    num_documents_all: int,
    num_documents_export_not_started: int,
    num_documents_in_export: int,
    num_documents_exported: int,
    num_documents_unknown: int,
    allow_unknown_documents: bool,
    strategy: DossierCloseStrategy,
    expected_read_dossier_close: bool,
):
    stats1 = DossierCloseReadyStats(
        num_documents_all=num_documents_all,
        num_documents_export_not_started=num_documents_export_not_started,
        num_documents_in_export=num_documents_in_export,
        num_documents_exported=num_documents_exported,
        num_documents_unknown=num_documents_unknown,
        num_original_files_in_processing=0,
        allow_unknown_documents=allow_unknown_documents,
        original_file_changed_timestamp=timezone.now(),
    )
    eval1 = evaluate_dossier_close(stats1, strategy)
    assert eval1.ready_for_close == expected_read_dossier_close
