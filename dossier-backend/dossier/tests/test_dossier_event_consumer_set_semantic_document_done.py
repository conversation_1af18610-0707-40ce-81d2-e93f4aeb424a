import pytest


from dossier.fakes import add_some_fake_semantic_documents
from dossier.management.commands.dossier_event_consumer_v2 import (
    set_semantic_document_export_done,
)
from dossier.models import DocumentCategory
from semantic_document.schemas import SemanticDocumentPDFResponseV1
from statemgmt.configurations.semantic_document_state_machine import (
    SemanticDocumentState,
    create_semantic_document_state_machine,
)
from statemgmt.models import Status
from workers.models import SemanticDocumentExport


# Mock the validate_state_transition function
def validate_state_transition(context, current_status, next_status):
    # For testing purposes, assume any transition from READY_FOR_EXPORT to EXPORT_AVAILABLE is valid
    if (
        current_status.key == SemanticDocumentState.READY_FOR_EXPORT.value
        and next_status.key == SemanticDocumentState.EXPORT_AVAILABLE.value
    ):
        return True
    else:
        raise Exception("Invalid state transition")


@pytest.fixture()
def prepare_data(synthetic_dossier):
    dossier = synthetic_dossier

    document_category = DocumentCategory.objects.filter(account=dossier.account).first()

    semantic_documents = add_some_fake_semantic_documents(
        dossier,
        num_docs=1,
        allow_empty_docs=False,
        valid_document_category_keys=[document_category.name],
        max_pages=5,
        min_num_pages=5,
    )

    # Create a SemanticDocumentExport
    semantic_document_export = SemanticDocumentExport.objects.create(
        semantic_document=semantic_documents[0]
    )

    body = SemanticDocumentPDFResponseV1(
        **{"semantic_document_pdf_request_uuid": str(semantic_document_export.uuid)}
    ).model_dump_json()

    return dossier, semantic_documents, semantic_document_export, body


@pytest.mark.django_db
def test_set_semantic_document_export_done_without_state_machine(prepare_data):
    # Create an account without active_semantic_document_work_status_state_machine
    dossier, semantic_documents, semantic_document_export, body = prepare_data

    # Call the function
    set_semantic_document_export_done(body)

    # Refresh from DB
    semantic_document_export.refresh_from_db()

    # Assert that 'done' is set
    assert semantic_document_export.done is not None

    # Assert that semantic_document.work_status is unchanged (since no state machine)
    assert semantic_documents[0].work_status is None


@pytest.fixture()
def prepare_data_state_machine(prepare_data):
    dossier, _, _, _ = prepare_data

    account = dossier.account

    # Create a StateMachine
    created_objects = create_semantic_document_state_machine()

    state_machine = created_objects["machine"]

    # Associate state machine with account
    account.active_semantic_document_work_status_state_machine = state_machine
    account.save()

    semantic_documents = add_some_fake_semantic_documents(dossier=dossier, num_docs=1)

    assert len(semantic_documents) == 1

    semantic_document = semantic_documents[0]

    semantic_document_export = SemanticDocumentExport.objects.create(
        semantic_document=semantic_document
    )

    # Prepare the body
    body = SemanticDocumentPDFResponseV1(
        **{"semantic_document_pdf_request_uuid": str(semantic_document_export.uuid)}
    ).model_dump_json()

    return dossier, semantic_document, state_machine, semantic_document_export, body


@pytest.mark.django_db
def test_set_semantic_document_export_done_with_state_machine_valid_transition(
    prepare_data_state_machine,
):
    # Create an account with active_semantic_document_work_status_state_machine
    # And a valid state transition (semantic_document.work_status = READY_FOR_EXPORT -> EXPORT_AVAILABLE)

    dossier, semantic_document, state_machine, semantic_document_export, body = (
        prepare_data_state_machine
    )

    semantic_document.work_status = Status.objects.get(
        key=SemanticDocumentState.READY_FOR_EXPORT.value, state_machine=state_machine
    )

    semantic_document.save()

    set_semantic_document_export_done(body)

    # Refresh from DB
    semantic_document_export.refresh_from_db()
    semantic_document.refresh_from_db()

    # Assert that 'done' is set
    assert semantic_document_export.done is not None

    # Assert that semantic_document.work_status is updated to EXPORT_AVAILABLE
    assert (
        semantic_document.work_status.key
        == SemanticDocumentState.EXPORT_AVAILABLE.value
    )


@pytest.mark.django_db
def test_set_semantic_document_export_done_with_state_machine_invalid_transition(
    prepare_data_state_machine,
):
    # Create an account with active_semantic_document_work_status_state_machine
    # And an invalid state transition (semantic_document.work_status = IN_FRONT_OFFICE NOT-> EXPORT_AVAILABLE)
    dossier, semantic_document, state_machine, semantic_document_export, body = (
        prepare_data_state_machine
    )

    set_semantic_document_export_done(body)

    # Refresh from DB
    semantic_document_export.refresh_from_db()
    semantic_document.refresh_from_db()

    # Assert that 'done' is set
    assert semantic_document_export.done is not None

    # Assert that semantic_document.work_status has not changed
    assert (
        semantic_document.work_status.key == SemanticDocumentState.IN_FRONT_OFFICE.value
    )
