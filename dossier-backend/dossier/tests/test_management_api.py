from typing import List

import pytest
from django.contrib.auth import get_user_model
from django.urls import reverse
from pydantic import TypeAdapter

from core.authentication import AuthenticatedClient
from dossier import schemas
from dossier.fakes import (
    add_some_fake_semantic_documents,
    load_initial_document_categories,
)
from dossier.helpers import get_all_s3_objects_in_bucket
from dossier.models import Account, JWK, BusinessCaseType
from dossier.schemas import AccountResponseSchema, JWKSchema, JWKSetSchema
from dossier.services import create_dossier
from projectconfig.jwk import KeyPair
from projectconfig.settings import (
    DEFAULT_VALID_UI_LANGUAGES,
    DEFAULT_VALID_DOSSIER_LANGUAGES,
)
from statemgmt.models import StateMachine

User = get_user_model()

pytestmark = pytest.mark.django_db


@pytest.fixture
def default_account_fixture():
    return {
        "created_at": "2022-05-12T06:12:14.366Z",
        "updated_at": "2022-05-12T06:12:14.366Z",
        "key": "default",
        "name": "HyppoDossier account",
        "default_bucket_name": "dms-default-bucket",
        "default_dossier_expiry_duration_days": 28,
        "max_dossier_expiry_duration_days": 365,
        "frontend_theme": "",
        "photo_album_docx_template": "",
        "valid_dossier_languages": DEFAULT_VALID_DOSSIER_LANGUAGES,
        "valid_ui_languages": DEFAULT_VALID_UI_LANGUAGES,
        "show_document_category_external": False,
        "show_business_case_type": False,
        "enable_button_create": True,
        "enable_feedback_form": False,
        "enable_download_original_file_link": True,
        "enable_show_deleted_elements": False,
        "enable_dossier_sorting": False,
        "enable_error_detail": True,
        "enable_dossier_search": False,
        "enable_zoom_feature": True,
        "enable_debug_document": True,
        "enable_download_dossier": True,
        "enable_download_document": False,
        "enable_icons_on_page_view": True,
        "enable_uploading_files": True,
        "enable_drag_and_drop_in_page_view": True,
        "enable_rendering_structure_tab": True,
        "enable_hovered_section_on_page_view": True,
        "enable_rendering_hurdles_tab": True,
        "enable_area_calculator": True,
        "enable_semantic_document_confidence": False,
        "enable_semantic_document_export": False,
        "enable_bekb_export": False,
    }


def test_management_role_auth(
    disable_structlog_for_testing, testuser1_client: AuthenticatedClient
):
    # Test that only jwt containing the user_role 'mgmt-api' are able to auth in
    result = testuser1_client.get(
        reverse("api:accounts"),
    )

    assert result.status_code == 401


def test_retrieve_account(mgmt_api_user_client: AuthenticatedClient):
    account = Account.objects.first()
    state_machine = StateMachine.objects.get(name="Dossier Status BEKB Initial")
    account.active_work_status_state_machine = state_machine
    account.save()
    result = mgmt_api_user_client.get(
        reverse("api:accounts", kwargs={"account_uuid": account.uuid}),
    )

    assert result.status_code == 200

    parsed = AccountResponseSchema.model_validate_json(result.content)

    assert parsed.uuid == account.uuid
    assert parsed.name == account.name
    assert parsed.active_work_status_state_machine == state_machine.uuid


def test_list_account(mgmt_api_user_client: AuthenticatedClient):
    result = mgmt_api_user_client.get(
        reverse("api:accounts"),
    )
    assert result.status_code == 200

    parsed = TypeAdapter(List[AccountResponseSchema]).validate_json(result.content)

    assert len(parsed) > 0

    assert len(parsed) == Account.objects.all().count()


def test_create_account_api(
    default_account_fixture, mgmt_api_user_client: AuthenticatedClient
):
    Account.objects.all().delete()

    post_data = schemas.AccountChangeSchema(**default_account_fixture)

    state_machine = StateMachine.objects.get(name="Dossier Status BEKB Initial")
    post_data.active_work_status_state_machine = state_machine.uuid

    result = mgmt_api_user_client.post(
        reverse("api:accounts"),
        data=post_data.model_dump_json(),
        content_type="application/json",
    )
    assert result.status_code == 201
    object = schemas.CreatedObjectReference.model_validate_json(result.content)

    assert object.uuid
    assert Account.objects.get(uuid=object.uuid)


def test_update_account(
    default_account_fixture, mgmt_api_user_client: AuthenticatedClient
):
    account = Account.objects.first()

    dict_data = {**default_account_fixture}
    dict_data.pop("key")
    dict_data["name"] = "Test name"

    post_data = schemas.AccountChangeSchema(**dict_data)

    state_machine = StateMachine.objects.get(name="Dossier Status BEKB Initial")
    post_data.active_work_status_state_machine = state_machine.uuid

    result = mgmt_api_user_client.put(
        reverse("api:accounts", kwargs={"account_uuid": account.uuid}),
        data=post_data.model_dump_json(),
        content_type="application/json",
    )

    assert result.status_code == 200

    parsed = AccountResponseSchema.model_validate_json(result.content)

    assert parsed.uuid == account.uuid
    assert parsed.name == "Test name"


def test_update_account_conflict(
    default_account_fixture, mgmt_api_user_client: AuthenticatedClient
):
    account = Account.objects.exclude(name="HyppoDossier account").first()

    dict_data = {**default_account_fixture}
    dict_data.pop("key")

    post_data = schemas.AccountChangeSchema(**dict_data)

    state_machine = StateMachine.objects.get(name="Dossier Status BEKB Initial")
    post_data.active_work_status_state_machine = state_machine.uuid

    result = mgmt_api_user_client.put(
        reverse("api:accounts", kwargs={"account_uuid": account.uuid}),
        data=post_data.model_dump_json(),
        content_type="application/json",
    )

    assert result.status_code == 409


def test_delete_account_synthetic_dossier(
    default_account_fixture,
    mgmt_api_user_client: AuthenticatedClient,
    temp_minio_bucket,
):

    # Test deleting a synthetic dossier - its missing all the associated dossier
    # and original files we would expect in a real dossier
    post_data = schemas.AccountChangeSchema(**default_account_fixture)

    post_data.default_bucket_name = temp_minio_bucket

    post_data.key = "default2"
    post_data.name = "HyppoDossier account2"

    state_machine = StateMachine.objects.get(name="Dossier Status BEKB Initial")
    post_data.active_work_status_state_machine = state_machine.uuid

    result = mgmt_api_user_client.post(
        reverse("api:accounts"),
        data=post_data.model_dump_json(),
        content_type="application/json",
    )
    assert result.status_code == 201
    new_account = Account.objects.get(name="HyppoDossier account2")

    initial_all_dossiers = get_all_s3_objects_in_bucket(
        dossier_bucket=temp_minio_bucket
    )

    dossier = create_dossier(
        new_account,
        "dossier with case",
        "de",
        User.objects.get(username="<EMAIL>"),
        businesscase_type_id=BusinessCaseType.objects.create(
            account=new_account, key="test business case type"
        ).uuid,
    )
    dossier.external_id = "test external id"

    dossier.save()

    load_initial_document_categories(account=new_account)
    add_some_fake_semantic_documents(dossier)

    result = mgmt_api_user_client.delete(
        reverse("api:accounts", kwargs={"account_uuid": result.json()["uuid"]}),
    )

    assert result.status_code == 200

    assert Account.objects.filter(name="HyppoDossier account2").exists() is False

    assert dossier.bucket == temp_minio_bucket

    # Assert that no. of s3 objects has not changed, since create and deletion
    assert set(initial_all_dossiers) == set(
        get_all_s3_objects_in_bucket(dossier_bucket=dossier.bucket)
    )


def test_get_jwk_from_account_empty(mgmt_api_user_client: AuthenticatedClient):
    account = Account.objects.first()
    result = mgmt_api_user_client.get(
        reverse("api:accounts-jwk", kwargs={"account_uuid": account.uuid}),
    )

    assert result.status_code == 200

    parsed = TypeAdapter(List[JWKSchema]).validate_json(result.content)
    assert parsed == []


def test_get_jwks_from_account(
    mock_jwks_public, mgmt_api_user_client: AuthenticatedClient
):
    # Get all JWKs associated with account
    account = Account.objects.first()
    jwk = JWK.objects.create(account=account, description="Foo", jwk=mock_jwks_public)

    JWKSchema.model_validate(jwk)
    result = mgmt_api_user_client.get(
        reverse("api:accounts-jwk", kwargs={"account_uuid": account.uuid}),
    )

    assert result.status_code == 200

    parsed = TypeAdapter(List[JWKSchema]).validate_json(result.content)
    assert parsed == [
        JWKSchema(
            account=JWKSchema.AccountUUIDKEYSchema(uuid=account.uuid, key=account.key),
            jwk=KeyPair(**mock_jwks_public),
            description="Foo",
            enabled=True,
        )
    ]


def test_get_jwk(mock_jwks_public, mgmt_api_user_client: AuthenticatedClient):
    account = Account.objects.first()
    jwk = JWK.objects.create(account=account, description="Foo", jwk=mock_jwks_public)

    JWKSchema.model_validate(jwk)
    result = mgmt_api_user_client.get(
        reverse("api:jwk", kwargs={"jwk_uuid": jwk.uuid}),
    )

    assert result.status_code == 200

    parsed = JWKSchema.model_validate_json(result.content)

    assert parsed == JWKSchema(
        account=JWKSchema.AccountUUIDKEYSchema(uuid=account.uuid, key=account.key),
        jwk=KeyPair(**mock_jwks_public),
        description="Foo",
        enabled=True,
    )


def test_get_jwks(mock_jwks_public, mgmt_api_user_client: AuthenticatedClient):
    # Get all JWKS
    account = Account.objects.first()

    JWK.objects.all().delete()

    jwk = JWK.objects.create(account=account, description="Foo", jwk=mock_jwks_public)

    JWKSchema.model_validate(jwk)
    result = mgmt_api_user_client.get(
        reverse("api:jwks"),
    )

    assert result.status_code == 200

    parsed = TypeAdapter(List[JWKSchema]).validate_json(result.content)
    assert parsed == [
        JWKSchema(
            account=JWKSchema.AccountUUIDKEYSchema(uuid=account.uuid, key=account.key),
            jwk=KeyPair(**mock_jwks_public),
            description="Foo",
            enabled=True,
        )
    ]


def test_add_jwk(mock_jwks_public, mgmt_api_user_client: AuthenticatedClient):
    account = Account.objects.first()

    result = mgmt_api_user_client.post(
        reverse("api:jwk"),
        data=JWKSetSchema(
            account=JWKSetSchema.AccountUUIDSchema(uuid=account.uuid),
            jwk=KeyPair(**mock_jwks_public),
            description="Foo",
            enabled=True,
        ).model_dump_json(),
        content_type="application/json",
    )

    assert result.status_code == 201

    parsed = JWKSchema.model_validate_json(result.content)

    assert parsed == JWKSchema(
        account=JWKSchema.AccountUUIDKEYSchema(uuid=account.uuid, key=account.key),
        jwk=KeyPair(**mock_jwks_public),
        description="Foo",
        enabled=True,
    )

    assert JWK.objects.filter(account=account).count() == 1


def test_add_jwk_conflict(mock_jwks_public, mgmt_api_user_client: AuthenticatedClient):
    account = Account.objects.first()
    JWK.objects.create(account=account, description="Foo", jwk=mock_jwks_public)

    result = mgmt_api_user_client.post(
        reverse("api:jwk"),
        data=JWKSetSchema(
            account=JWKSetSchema.AccountUUIDSchema(uuid=account.uuid),
            jwk=KeyPair(**mock_jwks_public),
            description="Foo",
            enabled=True,
        ).model_dump_json(),
        content_type="application/json",
    )

    assert result.status_code == 409


def test_delete_jwk(mock_jwks_public, mgmt_api_user_client: AuthenticatedClient):
    account = Account.objects.first()
    jwk = JWK.objects.create(account=account, description="Foo", jwk=mock_jwks_public)

    result = mgmt_api_user_client.delete(
        reverse("api:jwk", kwargs={"jwk_uuid": jwk.uuid}),
    )

    assert result.status_code == 200

    assert not JWK.objects.filter(account=account).exists()
