from datetime import datetime
from enum import Enum, IntEnum
from typing import List, Optional
from uuid import UUID

from pydantic import BaseModel, UUID4, Field

from dossier.schemas import PageObjectTitles, BoundingBox


class FinhurdleRef(BaseModel):
    semantic_document_title: str = Field()
    semantic_document_uuid: UUID4 = Field()
    semantic_page_number: int = Field()
    semantic_page_uuid: UUID4 = Field()
    page_object_uuid: UUID4 = Field()
    page_object_value: str = Field()
    bbox: BoundingBox = Field()


class FinHurdleGroup(BaseModel):
    key: str = Field()
    titles: PageObjectTitles = Field()
    finhurdle_refs: List[FinhurdleRef] = []


class DossierRoleSchema(BaseModel):
    uuid: UUID
    account_key: str
    key: str
    name_de: Optional[str] = None
    name_en: Optional[str] = None
    name_fr: Optional[str] = None
    name_it: Optional[str] = None
    user_selectable: bool
    show_separate_filter: bool
    user_uuid: UUID
    user_first_name: str
    user_last_name: str
    user_username: str


class Languages(str, Enum):
    GERMAN = "DE"
    ENGLISH = "EN"
    FRENCH = "FR"
    ITALIAN = "IT"


class DossierPropertiesResponse(BaseModel):
    name: str
    # Should businesscase_type be a key? in the model the key is not Unique
    businesscase_type_uuid: Optional[UUID] = None
    assignee: Optional[DossierRoleSchema] = None
    expiry_date: Optional[datetime] = None
    max_expiry_date: Optional[datetime] = None
    # dossier_language: Optional[Languages] = None


class DossierPropertiesChange(BaseModel):
    dossier_name: Optional[str] = None
    # Should businesscase_type be a key? in the model the key is not Unique
    expiry_date: Optional[datetime] = None
    businesscase_type_uuid: Optional[UUID] = None
    work_status_uuid: Optional[UUID] = None
    assignee_username: Optional[str] = None
    dossier_role: Optional[str] = None


class FileStatus(str, Enum):
    PROCESSING = "PROCESSING"
    PROCESSED = "PROCESSED"
    ERROR = "ERROR"


class FileExceptionType(str, Enum):
    PROCESSED = "Processed"
    EXTRACTED = "Extracted"
    ERROR = "Error"


class HypoDossierException(IntEnum):
    UNKNOWN_EXCEPTION = 1
    NOT_READABLE = 2
    PASSWORD_PROTECTED = 3
    UNSUPPORTED_FILETYPE = 4
    TOO_SMALL_FILE = 5
    XLSM_FILE_CANNOT_BE_CONVERTED = 6
    OCR_FILETYPE_PROCESSING = 7
    VIRUS_DETECTED = 8
    UNMAPPED_EXCEPTION = 999


class FileExceptionSchema(BaseModel):
    type: FileExceptionType
    exception_en: Optional[str] = None
    exception_de: Optional[str] = None
    exception_fr: Optional[str] = None
    exception_it: Optional[str] = None
    hypo_dossier_exception: Optional[HypoDossierException] = None


class ExtractedFileSchema(BaseModel):
    uuid: UUID
    path_from_original: str
    status: FileExceptionType
    file_name: str
    exception: Optional[FileExceptionSchema] = None


class OriginalFileSchema(BaseModel):
    uuid: UUID
    file_name: str
    status: FileStatus
    exception_en: Optional[str] = None
    exception_de: Optional[str] = None
    exception_fr: Optional[str] = None
    exception_it: Optional[str] = None
    extracted_files: List[ExtractedFileSchema]


class DossierProcessingDetails(BaseModel):
    dossier_uuid: UUID
    original_files: List[OriginalFileSchema]


class OptionalSemanticDocument(BaseModel):
    semantic_document_uuid: Optional[UUID4] = None


class SemanticDocumentPDFExportRequest(BaseModel):
    # Explicitly spell out that this is the export uuid and not the semantic_document uuid
    semantic_document_export_request_uuids: List[UUID]
