from core.helpers import remove_invalid_chars


def create_filenames_unique(semantic_documents_list):
    """
    Generate unique filenames for all semantic documents
    - add '.pdf'
    - add ' (1)', ' (2)', ... if titles are identical
    """

    ### TODO CONSOLIDATE_FILENAME
    # Step 1: Make a Dict[semantic document title] -> List[semantic_documents with that title]
    clean_doctitles = {}
    for semantic_document in semantic_documents_list:
        clean_doctitle = remove_invalid_chars(
            semantic_document["formatted_title"], " "
        ).strip()
        if clean_doctitle in clean_doctitles:
            clean_doctitles[clean_doctitle].append(semantic_document)
        else:
            clean_doctitles[clean_doctitle] = [semantic_document]

    # Step 2: Iterate over dict and assign filenames with or without counter in it
    for clean_doctitle, semantic_documents in clean_doctitles.items():
        if len(semantic_documents) > 1:
            # Multiple docs with the same title. We need a counter
            counter = 1
            for semantic_document in semantic_documents:
                semantic_document["filename"] = f"{clean_doctitle} ({counter}).pdf"
                counter += 1
        else:
            # Only one document with this title, no counter needed
            semantic_documents[0]["filename"] = f"{clean_doctitle}.pdf"

    return semantic_documents_list
