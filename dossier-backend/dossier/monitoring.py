import asyncio
import structlog
from django.conf import settings
from datetime import timedelta
from channels.db import database_sync_to_async
from django.utils import timezone

from dossier.models import OriginalFile, FileStatus, ExtractedFile

logger = structlog.get_logger()


@database_sync_to_async
def _get_unprocessed_extracted_files():
    max_processing_time = timezone.now() - timedelta(
        minutes=settings.FILE_STATUS_MAX_PROCESSING_TIME
    )

    # Check for unprocessed extracted files
    return list(
        ExtractedFile.objects.filter(
            status=FileStatus.PROCESSING, created_at__lt=max_processing_time
        ).prefetch_related("original_file", "dossier", "dossier__account")
    )


@database_sync_to_async
def _get_unprocessed_original_files():
    max_processing_time = timezone.now() - timedelta(
        minutes=settings.FILE_STATUS_MAX_PROCESSING_TIME
    )

    # Check for unprocessed original files
    return list(
        OriginalFile.objects.filter(
            status=FileStatus.PROCESSING, created_at__lt=max_processing_time
        ).prefetch_related("dossier", "dossier__account")
    )


async def check_file_statuses(run_once=False):
    # Function to check for unprocessed files and dispatch a log message if any are found
    # Logs will be read by loki / grafana and an alert created
    # This function is run as a background task in dossier_even_consumer.py
    while True:
        # Check for unprocessed extracted files
        unprocessed_extracted_files = await _get_unprocessed_extracted_files()
        for extracted_file in unprocessed_extracted_files:
            logger.info(
                "Unprocessed extracted file found",
                account_key=extracted_file.dossier.account.key,
                dossier_uuid=extracted_file.dossier.uuid,
                original_file_uuid=extracted_file.original_file.uuid,
                extracted_file_uuid=extracted_file.uuid,
                tag="unprocessed_extracted_file",
            )

        unprocessed_original_files = await _get_unprocessed_original_files()
        for original_file in unprocessed_original_files:
            logger.info(
                "Unprocessed original file found",
                account_key=original_file.dossier.account.key,
                dossier_uuid=original_file.dossier.uuid,
                original_file_uuid=original_file.uuid,
                tag="unprocessed_original_file",
            )

        if not unprocessed_extracted_files and not unprocessed_original_files:
            logger.info(
                "All files are processed within the maximal processing time",
                MAX_PROCESSING_TIME=settings.FILE_STATUS_MAX_PROCESSING_TIME,
            )
        if run_once:
            break
        await asyncio.sleep(settings.FILE_STATUS_CHECK_INTERVAL * 60)
