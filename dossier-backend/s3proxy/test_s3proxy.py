from multiprocessing import Pool
from pathlib import Path
from time import time
import base64
import os
from minio.helpers import md5sum_hash

import minio
import pytest
import requests
import structlog
from django.conf import settings

logger = structlog.get_logger(__name__)

pytest.skip("just for integration testing", allow_module_level=True)


S3_SSE_C_B64 = os.getenv("S3_SSE_C_B64")
S3_SSE_C_MD5_B64 = md5sum_hash(base64.b64decode(S3_SSE_C_B64))


@pytest.fixture(scope="session")
def minio_client():
    return minio.Minio(
        settings.S3_ENDPOINT,
        settings.S3_ACCESS_KEY,
        settings.S3_SECRET_KEY,
        secure=settings.S3_SECURE,
        region=settings.S3_REGION,
    )


@pytest.mark.integration
def test_direct_upload(minio_client):
    bucket = "production-v2-test-zkbat"
    object_name = "762ac12e-98d1-4271-981c-30fcf948af87/files/f064c883-209d-4c7a-b94f-846af565f066/small.zip"

    presigned_url = minio_client.presigned_put_object(bucket, object_name)

    logger.info("before replacement", presigned_url=presigned_url)

    with Path("/home/<USER>/Documents/zkb/package.zip").open("rb") as fp:
        start = time()
        res = requests.put(presigned_url, data=fp.read())
        logger.info("upload duration", duration=time() - start)
        res.raise_for_status()


@pytest.mark.integration
def test_upload_package(minio_client):
    bucket = "production-v2-test-zkbat"
    object_name = "762ac12e-98d1-4271-981c-30fcf948af87/files/f064c883-209d-4c7a-b94f-846af565f066/small.zip"

    presigned_url = minio_client.presigned_put_object(bucket, object_name)

    logger.info("before replacement", presigned_url=presigned_url)

    presigned_url = presigned_url.replace(
        settings.S3_ENDPOINT, settings.S3_ENDPOINT_PROXY
    )

    presigned_url = presigned_url.replace("https", "http")
    logger.info("after replacement", presigned_url=presigned_url)

    with Path("/home/<USER>/Documents/zkb/package.zip").open("rb") as fp:
        start = time()
        res = requests.put(presigned_url, data=fp.read())
        logger.info("upload duration", duration=time() - start)
        res.raise_for_status()


def download_file(presigned_url):
    start = time()
    res = requests.get(presigned_url)
    logger.info(
        "download duration",
        duration=time() - start,
        content_length=len(res.content),
    )


@pytest.mark.integration
def test_download_package(minio_client):
    bucket = "production-v2-test-zkbat"
    object_name = "762ac12e-98d1-4271-981c-30fcf948af87/files/f064c883-209d-4c7a-b94f-846af565f066/small.zip"

    logger.info("download file")

    presigned_url = minio_client.presigned_get_object(bucket, object_name)
    logger.info("before replacement", presigned_url=presigned_url)

    presigned_url = presigned_url.replace(
        settings.S3_ENDPOINT, settings.S3_ENDPOINT_PROXY
    )
    presigned_url = presigned_url.replace("https", "http")
    logger.info("after replacement", presigned_url=presigned_url)
    num_of_downloads = 1
    with Pool(num_of_downloads) as p:
        p.map(download_file, [presigned_url] * num_of_downloads)


def download_direct(presigned_url):
    start = time()
    res = requests.get(
        presigned_url,
        headers={
            "x-amz-server-side-encryption-customer-algorithm": "AES256",
            "x-amz-server-side-encryption-customer-key": S3_SSE_C_B64,
            "x-amz-server-side-encryption-customer-key-MD5": S3_SSE_C_MD5_B64,
        },
    )
    logger.info(
        "download duration",
        duration=time() - start,
        content_length=len(res.content),
    )


@pytest.mark.integration
def test_download_not_existing(minio_client):
    bucket = "production-v2-test-zkbat"
    object_name = "doestnotexist"

    logger.info("download file")

    presigned_url = minio_client.presigned_get_object(bucket, object_name)
    logger.info("before replacement", presigned_url=presigned_url)

    presigned_url = presigned_url.replace(
        settings.S3_ENDPOINT, settings.S3_ENDPOINT_PROXY
    )
    presigned_url = presigned_url.replace("https", "http")
    logger.info("after replacement", presigned_url=presigned_url)

    res = requests.get(presigned_url)
    assert res.status_code == 404
    assert res.content == b""
