import json

import django
from django.urls import reverse

from core.helpers import compare_datastructures
from zkb.schemas.data import DATA_PATH


def test_openapi_new_without_auth(client: django.test.client.Client):
    res = client.get(reverse("zkbopenapiwithoutauth"))
    assert res.status_code == 200
    assert "securitySchemes" not in res.json()

    # download e.g. from here: http://localhost:8000/partner/zkb/api/v1/openapi.json
    data = json.loads((DATA_PATH / "zkb_openapi_3_1_0_2025_02_13.json").read_text())

    # security and securitySchemes do not exist in version without auth
    compare_datastructures(
        res.json(), data, list_ignored_keys=["security", "securitySchemes"]
    )


def test_openapi_schema(client: django.test.client.Client):
    # Test to ensure that the underlying json Api schema has not changed
    res = client.get(reverse("zkb-api:openapi-json"))
    assert res.status_code == 200

    # download e.g. from here: http://localhost:8000/partner/zkb/api/v1/openapi.json
    with open(DATA_PATH / "zkb_openapi_3_1_0_2025_02_13.json") as file:
        data_2 = json.load(file)

    compare_datastructures(res.json(), data_2)
