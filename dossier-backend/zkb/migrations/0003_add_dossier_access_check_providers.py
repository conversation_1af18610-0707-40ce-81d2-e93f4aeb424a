# Generated by Django 4.2.13 on 2024-05-30 15:35
import logging
import structlog

from django.db import migrations


def create_dossier_access_check_providers(apps, schema_editor):
    DossierAccessCheckProvider = apps.get_model("dossier", "DossierAccessCheckProvider")

    zkb_external_check = "zkb external check (call zkb)"
    DossierAccessCheckProvider.objects.get_or_create(name=zkb_external_check)

    zkb_external_check_always_true = "zkb external check always true"
    DossierAccessCheckProvider.objects.get_or_create(
        name=zkb_external_check_always_true
    )

    zkb_external_check_always_false = "zkb external check always false"
    DossierAccessCheckProvider.objects.get_or_create(
        name=zkb_external_check_always_false
    )

    # logging.info(
    #     f"created dossier access check providers{DossierAccessCheckProvider.objects.count()}"
    # )


class Migration(migrations.Migration):

    dependencies = [
        ("zkb", "0002_accesscache_unique_user_id_dossier_id_access_cache"),
    ]

    operations = [
        migrations.RunPython(create_dossier_access_check_providers),
    ]
