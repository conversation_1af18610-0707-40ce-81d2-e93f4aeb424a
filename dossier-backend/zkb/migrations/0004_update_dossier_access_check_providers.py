# Generated by Django 4.2.15 on 2024-09-10 19:05

from django.db import migrations


def update_dossier_access_check_providers(apps, schema_editor):
    DossierAccessCheckProvider = apps.get_model("dossier", "DossierAccessCheckProvider")

    # Weird, sometimes this failes as name is already changed in tests
    external_check_true = DossierAccessCheckProvider.objects.filter(
        name="zkb external check always true"
    ).first()

    if external_check_true:
        external_check_true.name = "external check always true"
        external_check_true.save()

    external_check_always_false = DossierAccessCheckProvider.objects.filter(
        name="zkb external check always false"
    ).first()

    if external_check_always_false:
        external_check_always_false.name = "external check always false"
        external_check_always_false.save()


class Migration(migrations.Migration):

    dependencies = [
        ("zkb", "0003_add_dossier_access_check_providers"),
    ]

    operations = [
        migrations.RunPython(update_dossier_access_check_providers),
    ]
