{% extends 'stats/base.html' %}

{% block title %}{{ category.name }} Documents{% endblock %}

{% block extra_head %}
<script>
    function togglePages(id) {
        const element = document.getElementById(id);
        if (element.classList.contains('hidden')) {
            element.classList.remove('hidden');
        } else {
            element.classList.add('hidden');
        }
    }

    function showAllPages(documentUuid) {
        const allPagesContainer = document.getElementById(`all-pages-${documentUuid}`);
        const initialPagesContainer = allPagesContainer.previousElementSibling;
        const isWideLayout = {% if is_wide_layout %}true{% else %}false{% endif %};

        // Hide the initial pages container
        initialPagesContainer.classList.add('hidden');

        // Show the container with loading indicator
        allPagesContainer.classList.remove('hidden');

        // Fetch all pages for this document
        fetch(`/stats/api/document/${documentUuid}/pages/`)
            .then(response => response.json())
            .then(data => {
                // Clear loading indicator
                allPagesContainer.innerHTML = '';

                // Sort pages by number and create thumbnails
                const sortedPages = data.pages.sort((a, b) => a.number - b.number);
                sortedPages.forEach(page => {
                    const pageNumber = page.number + 1; // Adjust page number to start from 1
                    const thumbnailDiv = document.createElement('div');
                    thumbnailDiv.className = 'relative w-48 border border-gray-300 rounded overflow-hidden group shadow-md hover:shadow-lg transition-shadow duration-200';

                    const img = document.createElement('img');
                    img.src = page.image_url;
                    img.alt = `Page ${pageNumber}`;
                    img.className = 'w-full object-contain';
                    img.style.aspectRatio = '0.7';

                    const pageNumberDiv = document.createElement('div');
                    pageNumberDiv.className = 'absolute bottom-0 left-0 right-0 bg-black bg-opacity-60 text-white text-sm font-medium text-center py-1.5';
                    pageNumberDiv.textContent = `Page ${pageNumber}`;

                    thumbnailDiv.appendChild(img);
                    thumbnailDiv.appendChild(pageNumberDiv);
                    allPagesContainer.appendChild(thumbnailDiv);
                });
            })
            .catch(error => {
                console.error('Error fetching pages:', error);
                allPagesContainer.innerHTML = `
                    <div class="w-full text-center">
                        <p class="text-red-600">Error loading pages. Please try again.</p>
                    </div>
                `;
            });
    }
</script>
{% endblock %}

{% block content %}
<!-- Time Range Filter -->
<div class="bg-white shadow rounded-lg mb-6">
    <div class="px-4 py-5 sm:p-6">
        <form method="get" class="space-y-4" id="time-range-form">
            <input type="hidden" name="category_id" value="{{ category.id }}">
            <div class="flex flex-wrap gap-4 items-end">
                <div class="flex-1 min-w-[200px]">
                    <label for="{{ form.time_range.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">Time Range</label>
                    {{ form.time_range }}
                </div>

                <div class="flex-1 min-w-[200px] custom-date-range {% if form.time_range.value != 'custom' %}hidden{% endif %}">
                    <label for="{{ form.start_date.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
                    {{ form.start_date }}
                </div>

                <div class="flex-1 min-w-[200px] custom-date-range {% if form.time_range.value != 'custom' %}hidden{% endif %}">
                    <label for="{{ form.end_date.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">End Date</label>
                    {{ form.end_date }}
                </div>

                <div class="flex items-center">
                    <div class="flex items-center h-5">
                        {{ form.compare }}
                    </div>
                    <div class="ml-2 text-sm">
                        <label for="{{ form.compare.id_for_label }}" class="font-medium text-gray-700">Compare with previous period</label>
                    </div>
                </div>

                <div>
                    <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        Apply Filter
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const timeRangeSelect = document.getElementById('{{ form.time_range.id_for_label }}');
        const customDateRangeFields = document.querySelectorAll('.custom-date-range');

        timeRangeSelect.addEventListener('change', function() {
            if (this.value === 'custom') {
                customDateRangeFields.forEach(field => field.classList.remove('hidden'));
            } else {
                customDateRangeFields.forEach(field => field.classList.add('hidden'));
            }
        });
    });
</script>

<!-- Date Range Info -->
{% if start_date and end_date %}
<div class="bg-white shadow rounded-lg mb-6 p-4">
    <h3 class="text-lg font-medium text-gray-900">
        {% if start_date == end_date %}
            Documents for {{ start_date|date:"F j, Y" }}
        {% else %}
            Documents from {{ start_date|date:"F j, Y" }} to {{ end_date|date:"F j, Y" }}
        {% endif %}
    </h3>
</div>
{% endif %}

<div class="bg-white shadow overflow-hidden sm:rounded-lg">
    <div class="px-4 py-5 sm:px-6">
        <div class="flex justify-between items-center">
            <h2 class="text-lg leading-6 font-medium text-gray-900">
                {% if error %}
                    Category Not Found
                {% else %}
                    {{ category.name }} Documents
                    <span class="text-sm text-gray-500 ml-2">(Showing documents from all accounts with this category)</span>
                {% endif %}
            </h2>
            <a href="{% url 'stats:document_stats' %}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                Back to Statistics
            </a>
        </div>
        {% if not error %}
        <p class="mt-1 max-w-2xl text-sm text-gray-500">
            Showing {{ documents.paginator.count }} documents sorted by confidence (lowest first)
            {% if documents.paginator.num_pages > 1 %}
            - Page {{ documents.number }} of {{ documents.paginator.num_pages }}
            {% endif %}
        </p>
        {% endif %}
    </div>

    {% if error %}
    <div class="px-4 py-5 sm:px-6">
        <div class="bg-red-50 border-l-4 border-red-400 p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm text-red-700">
                        {{ error }}
                    </p>
                </div>
            </div>
        </div>
    </div>
    {% else %}
    <!-- Confidence Statistics -->
    <div class="px-4 py-5 sm:px-6 border-t border-gray-200">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <!-- Average Confidence -->
            <div class="bg-blue-50 overflow-hidden shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <dt class="text-sm font-medium text-gray-500 truncate">
                        Average Confidence
                    </dt>
                    <dd class="mt-1 text-3xl font-semibold text-blue-600">
                        {{ avg_confidence|floatformat:2 }}
                    </dd>
                </div>
            </div>

            <!-- Maximum Confidence -->
            <div class="bg-green-50 overflow-hidden shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <dt class="text-sm font-medium text-gray-500 truncate">
                        Maximum Confidence
                    </dt>
                    <dd class="mt-1 text-3xl font-semibold text-green-600">
                        {{ max_confidence|floatformat:2 }}
                    </dd>
                </div>
            </div>

            <!-- Minimum Confidence -->
            <div class="bg-yellow-50 overflow-hidden shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <dt class="text-sm font-medium text-gray-500 truncate">
                        Minimum Confidence
                    </dt>
                    <dd class="mt-1 text-3xl font-semibold text-yellow-600">
                        {{ min_confidence|floatformat:2 }}
                    </dd>
                </div>
            </div>
        </div>
    </div>

    <!-- Document List -->
    <div class="px-4 py-5 sm:px-6 border-t border-gray-200">
        <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
            Documents (Sorted by Confidence)
        </h3>

        {% if documents %}
        <div class="grid grid-cols-1 gap-6 {% if is_wide_layout %}max-w-7xl{% else %}max-w-5xl{% endif %} mx-auto">
            {% for document in documents %}
            <div class="bg-white rounded-lg shadow-md overflow-hidden border border-gray-200 flex flex-col">
                <!-- Document Header -->
                <div class="px-4 py-3 bg-gray-50 border-b border-gray-200">
                    <div class="flex justify-between items-start">
                        <div class="flex-1">
                            <h3 class="text-sm font-medium text-gray-900 truncate" title="{{ document.title }}">
                                {{ document.title }}
                            </h3>
                            <p class="text-xs text-gray-500 mt-1">
                                Dossier: {{ document.dossier.uuid }}
                            </p>
                        </div>
                        <div class="flex items-center ml-4">
                            {% if document.confidence_value >= 0.8 %}
                            <span class="px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800 flex items-center">
                                <div class="h-2 w-2 rounded-full bg-green-500 mr-1"></div>
                                {{ document.confidence_value|floatformat:2 }}
                            </span>
                            {% elif document.confidence_value >= 0.5 %}
                            <span class="px-2 py-1 text-xs font-medium rounded-full bg-yellow-100 text-yellow-800 flex items-center">
                                <div class="h-2 w-2 rounded-full bg-yellow-500 mr-1"></div>
                                {{ document.confidence_value|floatformat:2 }}
                            </span>
                            {% else %}
                            <span class="px-2 py-1 text-xs font-medium rounded-full bg-red-100 text-red-800 flex items-center">
                                <div class="h-2 w-2 rounded-full bg-red-500 mr-1"></div>
                                {{ document.confidence_value|floatformat:2 }}
                            </span>
                            {% endif %}
                        </div>
                    </div>
                    <div class="flex justify-between items-center mt-2 text-xs text-gray-500">
                        <span>{{ document.created_at|date:"M d, Y H:i" }}</span>
                        <div class="flex space-x-2">
                            <a href="/admin/semantic_document/semanticdocument/{{ document.uuid }}/change/" target="_blank" class="inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-1 focus:ring-indigo-500">
                                Details
                            </a>
                            <button type="button" onclick="togglePages('pages-{{ document.uuid }}')" class="inline-flex items-center px-2 py-1 border border-gray-300 text-xs font-medium rounded shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-1 focus:ring-indigo-500">
                                Pages ({{ document.semantic_pages.count }})
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Document Pages (initially hidden) -->
                <div id="pages-{{ document.uuid }}" class="hidden">
                    <div class="p-4 bg-gray-50">
                        <div class="flex flex-wrap gap-6 {% if is_wide_layout %}justify-start{% else %}justify-center{% endif %}">
                            {% with pages=document.semantic_pages.all|dictsort:"number"|slice:":5" %}
                            {% for page in pages %}
                            <div class="relative w-48 border border-gray-300 rounded overflow-hidden group shadow-md hover:shadow-lg transition-shadow duration-200">
                                <img src="{{ page.processed_page.image.fast_url }}" alt="Page {{ page.number }}" class="w-full object-contain" style="aspect-ratio: 0.7;">
                                <div class="absolute bottom-0 left-0 right-0 bg-black bg-opacity-60 text-white text-xs font-medium text-center py-1">
                                    Page {{ page.number|add:"1" }}
                                </div>
                            </div>
                            {% empty %}
                            <span class="text-sm text-gray-500">No pages available</span>
                            {% endfor %}
                            {% if document.semantic_pages.count > 5 %}
                            <div class="flex flex-col items-center justify-center w-48 border border-gray-300 rounded bg-gray-100 shadow-md" style="aspect-ratio: 0.7;">
                                <span class="text-xs text-gray-600 font-medium">+{{ document.semantic_pages.count|add:"-5" }}</span>
                                <button type="button" onclick="showAllPages('{{ document.uuid }}')" class="mt-2 inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-1 focus:ring-indigo-500">
                                    Show All
                                </button>
                            </div>
                            {% endif %}
                            {% endwith %}
                        </div>
                        <div id="all-pages-{{ document.uuid }}" class="hidden mt-4 flex flex-wrap gap-6 {% if is_wide_layout %}justify-start{% else %}justify-center{% endif %} border-t border-gray-200 pt-4">
                            <!-- All pages will be loaded here via JavaScript -->
                            <div class="w-full text-center">
                                <div class="inline-block animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-indigo-600"></div>
                                <p class="mt-2 text-xs text-gray-600">Loading all pages...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>

        <!-- Pagination Controls -->
        {% if is_paginated %}
        <div class="px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
            <div class="flex-1 flex justify-between sm:hidden">
                {% if documents.has_previous %}
                <a href="?page={{ documents.previous_page_number }}{% if query_params %}&{{ query_params }}{% endif %}" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    Previous
                </a>
                {% else %}
                <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-300 bg-gray-100 cursor-not-allowed">
                    Previous
                </span>
                {% endif %}

                {% if documents.has_next %}
                <a href="?page={{ documents.next_page_number }}{% if query_params %}&{{ query_params }}{% endif %}" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    Next
                </a>
                {% else %}
                <span class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-300 bg-gray-100 cursor-not-allowed">
                    Next
                </span>
                {% endif %}
            </div>

            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                    <p class="text-sm text-gray-700">
                        Showing
                        <span class="font-medium">{{ documents.start_index }}</span>
                        to
                        <span class="font-medium">{{ documents.end_index }}</span>
                        of
                        <span class="font-medium">{{ documents.paginator.count }}</span>
                        results
                    </p>
                </div>

                <div>
                    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                        {% if documents.has_previous %}
                        <a href="?page={{ documents.previous_page_number }}{% if query_params %}&{{ query_params }}{% endif %}" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                            <span class="sr-only">Previous</span>
                            <!-- Heroicon name: solid/chevron-left -->
                            <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                            </svg>
                        </a>
                        {% else %}
                        <span class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-gray-100 text-sm font-medium text-gray-300 cursor-not-allowed">
                            <span class="sr-only">Previous</span>
                            <!-- Heroicon name: solid/chevron-left -->
                            <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                            </svg>
                        </span>
                        {% endif %}

                        {% for i in documents.paginator.page_range %}
                            {% if documents.number == i %}
                            <span class="relative inline-flex items-center px-4 py-2 border border-indigo-500 bg-indigo-50 text-sm font-medium text-indigo-600">
                                {{ i }}
                            </span>
                            {% elif i > documents.number|add:'-3' and i < documents.number|add:'3' %}
                            <a href="?page={{ i }}{% if query_params %}&{{ query_params }}{% endif %}" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                                {{ i }}
                            </a>
                            {% endif %}
                        {% endfor %}

                        {% if documents.has_next %}
                        <a href="?page={{ documents.next_page_number }}{% if query_params %}&{{ query_params }}{% endif %}" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                            <span class="sr-only">Next</span>
                            <!-- Heroicon name: solid/chevron-right -->
                            <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                            </svg>
                        </a>
                        {% else %}
                        <span class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-gray-100 text-sm font-medium text-gray-300 cursor-not-allowed">
                            <span class="sr-only">Next</span>
                            <!-- Heroicon name: solid/chevron-right -->
                            <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                            </svg>
                        </span>
                        {% endif %}
                    </nav>
                </div>
            </div>
        </div>
        {% endif %}

        {% else %}
        <div class="text-center py-8">
            <p class="text-gray-500">No documents found for this category in the selected time period.</p>
        </div>
        {% endif %}
    </div>
    {% endif %}
</div>
{% endblock %}
