from abc import ABC, abstractmethod
from typing import List, Dict, Optional, Callable
import requests
from django.conf import settings
from pydantic import ConfigDict, BaseModel, Field
from rapidfuzz import fuzz

from cdp.models import FieldDefinition, RelevantFieldContext
from cdp.schemas import UnifiedRecommendationResponse, SourceDocumentDetails
import structlog

logger = structlog.get_logger()


class RecommendationRefinementStrategy(ABC):
    """
    Defines a contract for how to refine a list of recommendations
    given a particular field context.
    """

    @abstractmethod
    def refine(
        self,
        recommendations: List[UnifiedRecommendationResponse],
        field_context: Dict[str, str],
        context_field_weights: Dict[str, float],
    ) -> List[UnifiedRecommendationResponse]:
        pass


class FieldCtxInRecommendationSourceStrategy(RecommendationRefinementStrategy):
    """
    Filters recommendations by checking if the field context is present in the source document details.
    """

    def refine(
        self,
        recommendations: List[UnifiedRecommendationResponse],
        field_context: Dict[str, str],
        context_field_weights: Dict[str, float],
    ) -> List[UnifiedRecommendationResponse]:

        filtered_recommendations = []
        for rec in recommendations:
            filtered_rec_srcs = [
                src_doc
                for src_doc in rec.source.source_document_details
                if self.is_ctx_in_src_document(
                    field_context, src_doc, context_field_weights
                )
            ]
            if filtered_rec_srcs:
                rec.source.source_document_details = filtered_rec_srcs
                filtered_recommendations.append(rec)
        return filtered_recommendations

    @staticmethod
    def is_ctx_in_src_document(
        field_context: Dict[str, str],
        src_doc: SourceDocumentDetails,
        context_field_weights: Dict[str, float],
    ) -> bool:
        for sem_page in src_doc.semantic_pages:
            weighted_sum = 0.0
            total_weight = 0.0

            txt = get_searchable_txt_file(str(sem_page.searchable_txt_url))
            txt = txt.lower()

            for field, ctx in field_context.items():
                ctx = ctx.lower()
                match_ratio = fuzz.partial_ratio(txt, ctx)
                weight = context_field_weights.get(field, 1.0)
                weighted_sum += match_ratio * weight
                total_weight += weight

            if total_weight > 0:
                weighted_score = weighted_sum / total_weight
            else:
                weighted_score = 0

            if weighted_score >= settings.CDP_FIELD_CONTEXT_MATCH_THRESHOLD:
                return True
        return False


def get_searchable_txt_file(url: str) -> str:
    """
    Fetches the text content from a searchable text file URL.
    """
    response = requests.get(url)
    return response.text if response.status_code == 200 else ""


class StrategyRegistry(BaseModel):
    """Pydantic model where each field is a specific strategy."""

    txt_filter: Callable = Field(..., description="Strategy for matching person names")
    model_config = ConfigDict(arbitrary_types_allowed=True)


CDP_FIELD_CONTEXT_STRATEGIES = StrategyRegistry(
    txt_filter=FieldCtxInRecommendationSourceStrategy,  # checks if any of the field context values are present in the searchable text file of the source semantic document of the recommendations
)


class FieldContextHandler:
    """Handles extraction and filtering of recommendations based on field context."""

    def __init__(
        self,
        field_definition: FieldDefinition,
    ):
        self.context_field_weights = None
        self.field_definition = field_definition
        self.relevant_fields_for_context = RelevantFieldContext.objects.filter(
            field_definition=field_definition
        )

    def extract_relevant_context(
        self, field_context: Dict[str, str]
    ) -> Dict[str, Optional[str]]:
        relevant_fields = [
            str(ctx.relevant_field_definition)
            for ctx in self.relevant_fields_for_context
        ]

        return {
            field: field_context.get(field)
            for field in relevant_fields
            if field in field_context
        }

    def apply_field_context(
        self,
        recommendations: List[UnifiedRecommendationResponse],
        field_context: Dict[str, str],
        strategy: RecommendationRefinementStrategy,
    ) -> List[UnifiedRecommendationResponse]:
        relevant_context = self.extract_relevant_context(field_context)
        self.context_field_weights = {
            str(ctx.relevant_field_definition): ctx.weight
            for ctx in self.relevant_fields_for_context
        }
        if not relevant_context:
            return recommendations
        return strategy.refine(
            recommendations, relevant_context, self.context_field_weights
        )
