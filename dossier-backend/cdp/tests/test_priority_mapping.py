import pytest

from cdp.models import (
    FieldDefinition,
    PageObjectType,
    RelevantPageObject,
    DocumentCategory,
)
from cdp.services import get_priority_mapping


@pytest.fixture
def priority_mapping_data():
    """
    Creates FieldDefinition, PageObjectType, RelevantPageObject,
    DocumentCategory, and associated GenericPriorityMapping entries
    through the GenericRelation on RelevantPageObject.
    """
    # Create the FieldDefinition
    field_definition = FieldDefinition.objects.create(
        key="test_key", flavour="test_flavour"
    )

    # Create PageObjectTypes
    page_object = PageObjectType.objects.create(key="page_key")
    page_object2 = PageObjectType.objects.create(key="page_key2")

    # Create RelevantPageObjects
    relevant_page_object = RelevantPageObject.objects.create(
        field_definition=field_definition, page_object=page_object
    )
    relevant_page_object2 = RelevantPageObject.objects.create(
        field_definition=field_definition, page_object=page_object2
    )

    # Create DocumentCategories
    document_category1 = DocumentCategory.objects.create(key="test_category1")
    document_category2 = DocumentCategory.objects.create(key="test_category2")

    # Create GenericPriorityMapping entries via the GenericRelation
    # For relevant_page_object 1:
    relevant_page_object.generic_priority_mapping.create(
        document_category=document_category1, priority=1
    )
    relevant_page_object.generic_priority_mapping.create(
        document_category=document_category2, priority=2
    )

    # For relevant_page_object 2:
    relevant_page_object2.generic_priority_mapping.create(
        document_category=document_category1, priority=1
    )
    relevant_page_object2.generic_priority_mapping.create(
        document_category=document_category2, priority=2
    )

    # Return the identifying keys for subsequent tests
    return field_definition.key, field_definition.flavour


@pytest.mark.django_db
def test_returns_priority_mapping_for_valid_field_definition_document_category1_page_object_key1(
    priority_mapping_data,
):
    """
    Should return a priority of 1 for field_definition_key='test_key',
    field_definition_flavour='test_flavour',
    document_category='test_category1',
    page_object_key='page_key'.
    """
    field_definition_key, field_definition_flavour = priority_mapping_data

    result = get_priority_mapping(
        field_definition_key,
        field_definition_flavour,
        "test_category1",
        "page_key",
    )
    assert result == 1
    assert isinstance(result, int)


@pytest.mark.django_db
def test_returns_priority_mapping_for_valid_field_definition_document_category2_page_object_key1(
    priority_mapping_data,
):
    """
    Should return a priority of 2 for field_definition_key='test_key',
    field_definition_flavour='test_flavour',
    document_category='test_category2',
    page_object_key='page_key'.
    """
    field_definition_key, field_definition_flavour = priority_mapping_data

    result = get_priority_mapping(
        field_definition_key,
        field_definition_flavour,
        "test_category2",
        "page_key",
    )
    assert result == 2
    assert isinstance(result, int)


@pytest.mark.django_db
def test_returns_999_for_nonexistent_field_definition(priority_mapping_data):
    """
    Should return 999 if the FieldDefinition or other parameters do not exist.
    """
    # Use a non-existing combination
    result = get_priority_mapping(
        "nonexistent_key",
        "nonexistent_flavour",
        "test_category1",
        "page_key",
    )
    assert result == 999
