import pytest
from django.core.management import call_command
from pathlib import Path
from django.utils import timezone

from django.contrib.contenttypes.models import ContentType

from cdp.management.commands.truncate_cdp import truncate_cdp_tables
from cdp.models import (
    FieldSet,
    AssignedField,
    RelevantPageObject,
    GenericPriorityMapping,
    PageObjectType,
    DocumentCategory,
    FieldDefinition,
    ReturnType,
)


@pytest.mark.django_db
def test_export_and_import_field_sets_data(tmp_path, mocker):
    """
    Test that FieldSet data can be exported (including GenericPriorityMapping)
    and then imported back successfully.
    """
    # 1) Create initial data
    return_type = ReturnType.objects.create(
        key="return_type_key", description="Return Type Description"
    )
    field_set = FieldSet.objects.create(key="test_field_set")
    field_definition = FieldDefinition.objects.create(
        key="test_field_definition", flavour="test_flavour", return_type=return_type
    )
    AssignedField.objects.create(field_set=field_set, field_definition=field_definition)

    page_object = PageObjectType.objects.create(key="test_page_object_key")
    relevant_page_object = RelevantPageObject.objects.create(
        field_definition=field_definition, page_object=page_object
    )
    document_category = DocumentCategory.objects.create(key="test_document_category")

    relevant_page_object.generic_priority_mapping.create(
        document_category=document_category, priority=1
    )

    export_dir = tmp_path.parent.parent / "assets" / "field_set_data"
    export_dir.mkdir(parents=True, exist_ok=True)

    timestamp = timezone.now().strftime("%Y%m%d")
    expected_file_path = export_dir / f"field_set_export_{timestamp}.json"

    mocker.patch(
        "cdp.management.commands.export_all_field_sets.Path",
        return_value=Path(export_dir),
    )

    call_command("export_all_field_sets")
    assert expected_file_path.exists(), "Exported JSON file not found."

    truncate_cdp_tables()

    mocker.patch(
        "cdp.management.commands.import_all_field_sets.Path",
        return_value=Path(export_dir),
    )
    call_command("import_all_field_sets")

    imported_field_set = FieldSet.objects.get(key="test_field_set")
    assert imported_field_set is not None

    imported_assigned_field = AssignedField.objects.get(field_set=imported_field_set)
    imported_field_definition = imported_assigned_field.field_definition
    assert imported_field_definition.key == "test_field_definition"
    assert imported_field_definition.flavour == "test_flavour"
    assert imported_field_definition.return_type.key == "return_type_key"

    imported_relevant_page_object = RelevantPageObject.objects.get(
        field_definition=imported_field_definition
    )
    assert imported_relevant_page_object.page_object.key == "test_page_object_key"

    # Check the GenericPriorityMapping
    imported_priority_mapping = GenericPriorityMapping.objects.get(
        content_type=ContentType.objects.get_for_model(RelevantPageObject),
        object_id=imported_relevant_page_object.uuid,
    )
    assert imported_priority_mapping.document_category.key == "test_document_category"
    assert imported_priority_mapping.priority == 1
