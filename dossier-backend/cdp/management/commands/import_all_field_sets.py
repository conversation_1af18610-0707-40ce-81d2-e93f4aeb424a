import json
from pathlib import Path

import djclick as click
from django.contrib.contenttypes.models import ContentType

from cdp.models import (
    FieldSet,
    FieldDefinition,
    AssignedField,
    PageObjectType,
    RelevantPageObject,
    RelevantSemanticPage,
    RelevantSemanticDocument,
    DocumentCategory,
    GenericPriorityMapping,
    ReturnType,
    RelevantFieldContext,
)


@click.command()
@click.option(
    "--file",
    type=click.Path(exists=True),
    help="Path to the JSON file to import. If not provided, uses the latest file in `assets/field_set_data`.",
)
@click.option(
    "--delete-dangling-field-definitions/--no-delete-dangling-field-definitions",
    is_flag=True,
    default=False,
    help="Delete field definitions not present in the JSON file.",
)
@click.option(
    "--delete-dangling-priority-mappings/--no-delete-dangling-priority-mappings",
    default=True,
    help="Delete priority mappings not present in the JSON file.",
)
def command(file, delete_dangling_field_definitions, delete_dangling_priority_mappings):
    """
    Import FieldSet data from a JSON file, including GenericPriorityMapping for
    RelevantPageObject, RelevantSemanticPage, and RelevantSemanticDocument.
    """
    field_set_data_path = (
        Path(__file__).resolve().parent.parent / "assets" / "field_set_data"
    )
    file_path = file

    if not file_path:
        json_files = list(field_set_data_path.glob("field_set_export_*.json"))
        if not json_files:
            click.secho(
                "No JSON files found in the field_set_data_path directory.", fg="red"
            )
            return

        # Sort files by timestamp in the filename (newest first)
        json_files.sort(key=lambda x: x.stem, reverse=True)
        file_path = json_files[0]

    click.secho(f"Importing data from {file_path}", fg="green")
    with open(file_path) as json_file:
        data = json.load(json_file)

    # Handle the cleanup of DB FieldDefinitions not found in the JSON
    handle_field_definitions_not_in_db(data, delete_dangling_field_definitions)

    # Populate the data
    populate_field_set_data(data, delete_dangling_priority_mappings)

    click.secho(
        "Nested data with PriorityMappings has been imported successfully", fg="green"
    )


def handle_field_definitions_not_in_db(data, delete_dangling_field_definitions):
    """
    Identifies FieldDefinitions in the DB that do not appear in the JSON file.
    Optionally deletes them if `--delete-dangling-field-definitions` is specified.
    """
    json_field_definitions = set()
    for field_set_data in data:
        for assigned_field_data in field_set_data["assigned_fields"]:
            fd_key = assigned_field_data["field_definition_key"]
            fd_flavour = assigned_field_data.get("field_definition_flavour")
            json_field_definitions.add((fd_key, fd_flavour))

    # Collect field definitions from DB
    db_field_definitions = set(FieldDefinition.objects.values_list("key", "flavour"))

    # Identify dangling
    field_definitions_not_in_db = db_field_definitions - json_field_definitions
    if field_definitions_not_in_db:
        click.secho(
            "Field definitions in DB not found in JSON: "
            f"{field_definitions_not_in_db}",
            fg="yellow",
        )
        if delete_dangling_field_definitions:
            for fd_key, fd_flavour in field_definitions_not_in_db:
                FieldDefinition.objects.filter(key=fd_key, flavour=fd_flavour).delete()
            click.secho("Dangling field definitions deleted.", fg="green")


def populate_field_set_data(data, delete_dangling_priority_mappings):
    """
    Iterates through the JSON structure to create/update FieldSets, AssignedFields,
    and all relevant objects. Also creates GenericPriorityMapping records
    for each relevant object type.

    Additionally, removes any leftover GenericPriorityMapping rows
    (for those same objects) that do not appear in the JSON.

    If `delete_dangling_priority_mappings` is True:
      deletes the GenericPriorityMapping records present in the DB but not in the JSON.
    If `delete_dangling_priority_mappings` is False:
      raises a warning for leftover GenericPriorityMapping records.
    """

    ct_pageobject = ContentType.objects.get_for_model(RelevantPageObject)
    ct_semanticpage = ContentType.objects.get_for_model(RelevantSemanticPage)
    ct_semanticdoc = ContentType.objects.get_for_model(RelevantSemanticDocument)

    for field_set_data in data:
        field_set_key = field_set_data["key"]
        field_set, _created = FieldSet.objects.get_or_create(key=field_set_key)

        fd_uuids_in_json = []
        new_gpm_combos = set()
        existing_gpm_combos = set()

        for assigned_field_data in field_set_data["assigned_fields"]:
            field_definition = handle_field_definition(assigned_field_data)
            AssignedField.objects.get_or_create(
                field_set=field_set, field_definition=field_definition
            )

            fd_uuids_in_json.append(field_definition.uuid)

            handle_relevant_objects(
                assigned_field_data,
                dict_key="relevant_page_objects",
                object_model=RelevantPageObject,
                content_type=ct_pageobject,
                field_definition=field_definition,
                new_gpm_combos=new_gpm_combos,
            )

            handle_relevant_objects(
                assigned_field_data,
                dict_key="relevant_semantic_pages",
                object_model=RelevantSemanticPage,
                content_type=ct_semanticpage,
                field_definition=field_definition,
                new_gpm_combos=new_gpm_combos,
            )

            handle_relevant_objects(
                assigned_field_data,
                dict_key="relevant_semantic_documents",
                object_model=RelevantSemanticDocument,
                content_type=ct_semanticdoc,
                field_definition=field_definition,
                new_gpm_combos=new_gpm_combos,
            )

            handle_relevant_field_contexts(field_definition, assigned_field_data)

        existing_gpm_combos |= gather_existing_db_combos(
            RelevantPageObject, ct_pageobject, fd_uuids_in_json
        )
        existing_gpm_combos |= gather_existing_db_combos(
            RelevantSemanticPage, ct_semanticpage, fd_uuids_in_json
        )
        existing_gpm_combos |= gather_existing_db_combos(
            RelevantSemanticDocument, ct_semanticdoc, fd_uuids_in_json
        )

        leftover_combos = existing_gpm_combos - new_gpm_combos
        if leftover_combos:
            click.secho(
                "WARNING: The following GenericPriorityMappings are no longer in the JSON for FieldSet "
                f"{field_set_key}:",
                fg="yellow",
            )
            for combo in leftover_combos:
                ct_id, obj_id, doc_cat_id = combo
                qs = GenericPriorityMapping.objects.filter(
                    content_type_id=ct_id,
                    object_id=obj_id,
                    document_category_id=doc_cat_id,
                )
                for gpm in qs:
                    click.secho(
                        f"GPM: content_type={gpm.content_type}, "
                        f"object_id={gpm.object_id}, "
                        f"document_category={gpm.document_category}, "
                        f"priority={gpm.priority}",
                        fg="yellow",
                    )
                if delete_dangling_priority_mappings:
                    qs.delete()
                    click.secho(
                        "Deleted the dangling GenericPriorityMappings.", fg="green"
                    )


def handle_field_definition(assigned_field_data):
    """
    Create or update a FieldDefinition & ReturnType from a JSON assigned_field_data dict.
    """
    fd_key = assigned_field_data["field_definition_key"]
    fd_flavour = assigned_field_data.get("field_definition_flavour")
    fd_return_type_key = assigned_field_data.get("field_definition_return_type")

    return_type = None
    if fd_return_type_key:
        return_type, _ = ReturnType.objects.get_or_create(key=fd_return_type_key)

    field_definition, _ = FieldDefinition.objects.get_or_create(
        key=fd_key,
        flavour=fd_flavour,
        defaults={"return_type": return_type},
    )
    if field_definition.return_type != return_type:
        field_definition.return_type = return_type
        field_definition.save()

    return field_definition


def handle_relevant_objects(
    assigned_field_data,
    dict_key,
    object_model,
    content_type,
    field_definition,
    new_gpm_combos,
):
    """
    Reads `dict_key` from assigned_field_data (like 'relevant_page_objects'),
    creates/updates the model (`object_model`), and then updates/creates
    GenericPriorityMapping entries using `content_type`.

    new_gpm_combos is a set() we update with each (content_type_id, object_id, document_category_id).
    """

    objs_data = assigned_field_data.get(dict_key, [])
    for obj_data in objs_data:
        if dict_key == "relevant_page_objects":
            page_object_key = obj_data["page_object_key"]
            page_object, _ = PageObjectType.objects.get_or_create(key=page_object_key)
            relevant_obj, _ = object_model.objects.get_or_create(
                field_definition=field_definition,
                page_object=page_object,
            )
        elif dict_key in ("relevant_semantic_pages", "relevant_semantic_documents"):
            rel_type = obj_data["relevant_object_type"]
            relevant_obj, _ = object_model.objects.get_or_create(
                field_definition=field_definition,
                relevant_object_type=rel_type,
            )
        else:
            # If we add more object types in future
            continue

        # handle the "priority_mappings"
        for pm_data in obj_data.get("priority_mappings", []):
            doc_cat_key = pm_data["document_category_key"]
            priority = pm_data["priority"]
            doc_cat, _ = DocumentCategory.objects.get_or_create(key=doc_cat_key)

            gpm, _ = GenericPriorityMapping.objects.update_or_create(
                content_type=content_type,
                object_id=relevant_obj.uuid,
                document_category=doc_cat,
                defaults={"priority": priority},
            )
            new_gpm_combos.add(
                (gpm.content_type_id, gpm.object_id, gpm.document_category_id)
            )


def handle_relevant_field_contexts(field_definition, assigned_field_data):
    context_list = assigned_field_data.get("relevant_field_contexts", [])
    for ctx_data in context_list:
        other_key = ctx_data["relevant_field_definition_key"]
        other_flavour = ctx_data.get("relevant_field_definition_flavour")
        weight = ctx_data.get("weight", 1.0)
        other_fd, _ = FieldDefinition.objects.get_or_create(
            key=other_key,
            flavour=other_flavour,
        )
        rfc, _ = RelevantFieldContext.objects.update_or_create(
            field_definition=field_definition,
            relevant_field_definition=other_fd,
            defaults={"weight": weight},
        )


def gather_existing_db_combos(object_model, content_type, fd_uuids_in_json):
    """
    Finds existing GPM combos for objects of type `object_model` referencing
    the given field_definition UUIDs.
    Returns a set of (content_type_id, object_id, document_category_id).
    """
    combos = set()

    # For example, if object_model is RelevantPageObject, we filter by field_definition__uuid__in=fd_uuids_in_json
    objs_qs = object_model.objects.filter(field_definition__uuid__in=fd_uuids_in_json)

    existing_db_gpm = GenericPriorityMapping.objects.filter(
        content_type=content_type,
        object_id__in=objs_qs.values_list("uuid", flat=True),
    )
    for gpm in existing_db_gpm:
        combos.add((gpm.content_type_id, gpm.object_id, gpm.document_category_id))

    return combos
