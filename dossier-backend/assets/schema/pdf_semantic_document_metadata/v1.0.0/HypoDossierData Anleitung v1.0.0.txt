HypoDossier Data - Dokumentation Metadaten

Die Klassifikation der Dokumente wie sie in HypoDossier erkannt werden, wird automatisch in jedem PDF abgespeichert, das von HypoDossier generiert wird. Die Metadaten sind in einem Attachment zum PDF enthalten, das den Namen HypoDossierData.json trägt. Das Schema zur aktuellen Version ist in 'hypodossier_semantic_document_export_schema_v1.0.0' dokumentiert.

In Schema Version 1.0.0 gibt es folgende Felder:
- version: Version des Export-Schemas, zukünftige Versionen können zusätzliche Felder enthalten
- title: Titel des Dokuments, wie in HypoDossier generiert resp. durch den User überarbeitet (Titels wird in der Sprache des Dossiers generiert)
- document_category_name: Eindeutiger Identifier der Dokumentenkategorie im Aktenplan. Dieser wird sich nicht ändern und sollte zwingend zur eindeutigen Identifikation der Dokumentenkategorie verwendet werden. Alle möglichen Werte sind in 220630_HypoDossier_Spezifikation_Aktenplan.xlsx in der Spalte "Name" aufgeführt. Zukünftig können neue Werte hinzukommen, es werden aber keine gelöscht.
- document_category_title_de: Bezeichnung der Dokumentenkategorie in Deutsch. Kann zukünftig ändern.
- document_category_title_en: Bezeichnung der Dokumentenkategorie in Englisch. Kann zukünftig ändern.
- document_category_title_fr: Bezeichnung der Dokumentenkategorie in Französisch. Kann zukünftig ändern.
- document_category_title_it: Bezeichnung der Dokumentenkategorie in Italienisch. Kann zukünftig ändern.

Beispiel HypoDossierData.json:

{
    "version": "1.0.0",
    "uuid": "34aa04d5-1d18-4224-8d7f-033b1731da49",
    "title": "615 Fotos Liegenschaft IMG_7717",
    "document_category_name": "PROPERTY_PHOTOS",
    "document_category_title_de": "Fotos Liegenschaft",
    "document_category_title_en": "Property Photos",
    "document_category_title_fr": "Photos immeuble",
    "document_category_title_it": "Foto dell immobilie"
}

Zugriff auf document_category_name:
PDF Dokument öffnen -> Attachment 'HypoDossierData.json' öffnen und als JSON parsen -> Feld 'document_category_name' auslesen

