"""
Django settings for backend project.

Generated by 'django-admin startproject' using Django 3.2.1.

For more information on this file, see
https://docs.djangoproject.com/en/3.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/3.2/ref/settings/
"""

import os
import re
import sys
from distutils.util import strtobool
from pathlib import Path
from typing import Optional
from urllib.parse import urljoin

import structlog

# import sentry_sdk
from dotenv import load_dotenv
from structlog.dev import RichTracebackFormatter

from projectconfig.jwk import load_jwk_from_env, JWKPairList

# from sentry_sdk.integrations.django import DjangoIntegration

load_dotenv()

# this must be set before numpy is used according to https://stackoverflow.com/questions/52026652/openblas-blas-thread-init-pthread-create-resource-temporarily-unavailable
# Otherwise you can have this exception: 'OpenBLAS blas_thread_init: pthread_create failed for thread 1 of 2: Operation not permitted'
if "OPENBLAS_INITIALIZED" in os.environ and os.environ["OPENBLAS_NUM_THREADS"] == "1":
    OPENBLAS_INITIALIZED = True
else:
    OPENBLAS_INITIALIZED = False

# sentry_sdk.init(
#     dsn="https://<EMAIL>/2",
#     integrations=[DjangoIntegration()],
#
#     # Set traces_sample_rate to 1.0 to capture 100%
#     # of transactions for performance monitoring.
#     # We recommend adjusting this value in production.
#     traces_sample_rate=1.0,
#
#     # If you wish to associate users to errors (assuming you are using
#     # django.contrib.auth) you may enable sending PII data.
#     send_default_pii=True,
#     environment=os.getenv('SENTRY_ENVIRONMENT')
# )

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/3.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = os.getenv("SECRET_KEY")

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = int(os.environ.get("DEBUG", default=0))

DEBUG_ENABLE_SILK = strtobool(os.environ.get("DEBUG_ENABLE_SILK", default="False"))

SILKY_PYTHON_PROFILER = DEBUG_ENABLE_SILK

if DEBUG_ENABLE_SILK:
    SILK_INTERCEPT_PERCENT = 100

ALLOWED_HOSTS = os.getenv("DJANGO_ALLOWED_HOSTS", "localhost").split(" ")

# Namespace, e.g. BEKB, SWISSFEX, etc
# Inserted into RabbitMQ routing keys, as rabbitmq is currently shared between instances
DEPLOYMENT_NAMESPACE = os.getenv("DEPLOYMENT_NAMESPACE")


# CORS_ORIGIN_ALLOW_ALL = True

CORS_ALLOWED_ORIGIN_REGEXES = [
    re.compile(entry)
    for entry in os.getenv(
        "DJANGO_CORS_ORIGIN_WHITELIST", "http://localhost:8000"
    ).split(" ")
]

CSRF_TRUSTED_ORIGINS = [
    "https://" + re.sub(r"^https?://", "", host) for host in ALLOWED_HOSTS
]

# Whether to use keycloak to login into the django admin
ENABLE_ADMIN_KEYCLOAK_LOGIN = strtobool(
    os.environ.get("ENABLE_ADMIN_KEYCLOAK_LOGIN", "False")
)

# Application definition

INSTALLED_APPS = [
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django_structlog",
    "whitenoise.runserver_nostatic",
    "django.contrib.staticfiles",
    *(["silk"] if DEBUG_ENABLE_SILK else []),
    "django_json_widget",
    "hyrabbit",
    "tailwind",
    "channels",
    "nested_admin",
    "adminsortable",
    "colorfield",
    *(
        [
            "allauth",
            "allauth.account",
            "allauth.socialaccount",
            "allauth.socialaccount.providers.openid_connect",
            "adminauth",
        ]
        if ENABLE_ADMIN_KEYCLOAK_LOGIN
        else []
    ),
    "events",
    "statemgmt",
    "dossier",
    "processed_file",
    "semantic_document",
    "image_exporter",
    "dossier_zipper",
    "doccheck",
    "corsheaders",
    "workers",
    "bekb",
    "zkb",
    "swissfex",
    "import_export",  # Allow to export admin views as json/...
    "rangefilter",  # Allow to filter dates in admin by range
    "e2e",
    "bcge",
    "clientis",
    "finnova",
    "hypohaus",
    "hypoteq",
    "trial",
    "tophypo",
    "vontobel",
    "vz",
    "pentest",
    "gputest",
    "cdp",
    "stats",
    "theme",
]

MIDDLEWARE = [
    "corsheaders.middleware.CorsMiddleware",
    "django.middleware.security.SecurityMiddleware",
    "django_structlog.middlewares.RequestMiddleware",
    "whitenoise.middleware.WhiteNoiseMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    *(["silk.middleware.SilkyMiddleware"] if DEBUG_ENABLE_SILK else []),
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
    "projectconfig.error_handlers.HttpExceptionMiddleware",
    "projectconfig.logging_middleware.BadRequestLoggerMiddleware",
    *(
        ["allauth.account.middleware.AccountMiddleware"]
        if ENABLE_ADMIN_KEYCLOAK_LOGIN
        else []
    ),
]

ROOT_URLCONF = "projectconfig.urls"

SITE_ID = 1

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [os.path.join(BASE_DIR, "templates")],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

# WSGI_APPLICATION = 'projectconfig.wsgi.application'
ASGI_APPLICATION = "projectconfig.asgi.application"

# Database
# https://docs.djangoproject.com/en/3.2/ref/settings/#databases

DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.postgresql_psycopg2",
        "NAME": os.getenv("POSTGRES_DB"),
        "USER": os.getenv("POSTGRES_USER"),
        "PASSWORD": os.getenv("POSTGRES_PASSWORD"),
        "HOST": os.getenv("POSTGRES_HOST"),
        "PORT": os.getenv("POSTGRES_PORT", default="5432"),
    }
}


# Cache settings for CDP
CDP_SEMANTIC_PAGES_CACHE_TIMEOUT = int(
    os.getenv("CDP_SEMANTIC_PAGES_CACHE_TIMEOUT", "3600")
)  # 1 hour
CDP_SEMANTIC_PAGE_PAGE_OBJECT_DETAILS_CACHE_TIMEOUT = int(
    os.getenv("CDP_SEMANTIC_PAGE_PAGE_OBJECT_DETAILS_CACHE_TIMEOUT", "64800")
)  # 18 hours
CDP_CACHE_MAX_ENTRIES = int(
    os.getenv("CDP_CACHE_MAX_ENTRIES", "512")
)  # Maximum number of cache entries

DEFAULT_CACHE_MAX_ENTRIES = int(
    os.getenv("DEFAULT_CACHE_MAX_ENTRIES", "512")
)  # Maximum number of cache entries for default cache

CACHES = {
    "default": {
        "BACKEND": "django.core.cache.backends.locmem.LocMemCache",
        "LOCATION": "unique-snowflake",
        "OPTIONS": {
            "MAX_ENTRIES": DEFAULT_CACHE_MAX_ENTRIES,  # Maximum number of items in cache
        },
    },
    "cdp-cache": {
        "BACKEND": "django.core.cache.backends.locmem.LocMemCache",
        "LOCATION": "cdp-snowflake",
        "OPTIONS": {
            "MAX_ENTRIES": CDP_CACHE_MAX_ENTRIES,  # Maximum number of items in cache
        },
    },
}

# Password validation
# https://docs.djangoproject.com/en/3.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]

# Internationalization
# https://docs.djangoproject.com/en/3.2/topics/i18n/

LANGUAGE_CODE = "en-us"

USE_TZ = True  # Enable timezone-aware datetimes, datetimes are stored in UTC in DB
TIME_ZONE = "Europe/Zurich"  # Django converts to/from UTC when writing/reading the DB


USE_I18N = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/3.2/howto/static-files/

STATIC_URL = "/static/"

STATIC_ROOT = os.path.join(BASE_DIR, "staticfiles")

# Default primary key field type
# https://docs.djangoproject.com/en/3.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"
DATA_UPLOAD_MAX_MEMORY_SIZE = 1024 * 1024 * 80

S3_ENDPOINT = os.getenv("S3_ENDPOINT")
S3_ENDPOINT_PROXY = os.getenv("S3_ENDPOINT_PROXY")
S3_SSE_C_B64 = os.getenv("S3_SSE_C_B64")
S3_ENDPOINT_URL = os.environ.get("ADMIN_S3_ENDPOINT_URL")
# important for shared urls, else it is very slow
S3_REGION = os.getenv("S3_REGION", default="ch-dk-2")

S3_ACCESS_KEY = os.environ.get("S3_ACCESS_KEY")
S3_SECRET_KEY = os.environ.get("S3_SECRET_KEY")
S3_SECURE = strtobool(os.environ.get("S3_SECURE", default="True"))

S3_URL_EXPIRATION_SECONDS = int(os.getenv("S3_URL_EXPIRATION_SECONDS", "86400"))


KEYCLOAK_REALM_ENDPOINT = os.getenv(
    "KEYCLOAK_REALM_ENDPOINT", "https://auth.hypo.duckdns.org/realms/dev-hypodossier/"
)

ADMIN_KEYCLOAK_REALM_ENDPOINT = os.getenv(
    "ADMIN_KEYCLOAK_REALM_ENDPOINT",
    "https://auth.hypo.duckdns.org/realms/dev-hypodossier/",
)

if ENABLE_ADMIN_KEYCLOAK_LOGIN:
    AUTHENTICATION_BACKENDS = [
        # `allauth` specific authentication methods, such as login by e-mail
        "allauth.account.auth_backends.AuthenticationBackend"
    ]
else:
    AUTHENTICATION_BACKENDS = [
        # Needed to login by username in Django admin
        "django.contrib.auth.backends.ModelBackend",
    ]

# These two (forward host and secure proxy) are needed so that request.build_absolute_uri
# correctly returns a https url for keycloak logout
USE_X_FORWARDED_HOST = True
SECURE_PROXY_SSL_HEADER = ("HTTP_X_FORWARDED_PROTO", "https")

# end_session_endpoint
if ENABLE_ADMIN_KEYCLOAK_LOGIN:
    ACCOUNT_DEFAULT_HTTP_PROTOCOL = os.getenv(
        "ADMIN_KEYCLOAK_ACCOUNT_DEFAULT_HTTP_PROTOCOL", default="https"
    )
    ACCOUNT_DEFAULT_AUTHENTICATION_PROVIDER = "openid_connect"

    ADMIN_KEYCLOAK_CLIENT_ID = os.getenv("ADMIN_KEYCLOAK_CLIENT_ID", "django-admin")

    SOCIALACCOUNT_PROVIDERS = {
        "openid_connect": {
            "APPS": [
                {
                    "provider_id": "keycloak",
                    "name": "HypoDossier Keycloak",
                    "client_id": os.getenv("ADMIN_KEYCLOAK_CLIENT_ID", "django-admin"),
                    "secret": os.getenv("ADMIN_KEYCLOAK_SECRET_KEY"),
                    "settings": {
                        "server_url": urljoin(
                            ADMIN_KEYCLOAK_REALM_ENDPOINT,
                            ".well-known/openid-configuration",
                        ),
                    },
                }
            ]
        }
    }

    LOGIN_URL = "/accounts/oidc/keycloak/login/"

    SOCIALACCOUNT_LOGIN_ON_GET = True
    SOCIALACCOUNT_LOGOUT_ON_GET = True
    SOCIALACCOUNT_STORE_TOKENS = True

    ACCOUNT_ALLOW_REGISTRATION = False

    LOGIN_REDIRECT_URL = "/admin"

    # The CustomAccountAdapter disables account registration
    SOCIALACCOUNT_ADAPTER = "adminauth.adapters.CustomAccountAdapter"

# FILE FIELD
AWS_ACCESS_KEY_ID = os.getenv("ADMIN_S3_ACCESS_KEY")
AWS_SECRET_ACCESS_KEY = os.getenv("ADMIN_S3_SECRET_KEY")
AWS_S3_ENDPOINT_URL = os.getenv("ADMIN_S3_ENDPOINT_URL")

RABBIT_URL = os.getenv("RABBIT_URL")

# Channels
CHANNEL_LAYERS = {
    "default": {
        "BACKEND": "channels_rabbitmq.core.RabbitmqChannelLayer",
        "CONFIG": {
            "host": RABBIT_URL,
        },
    },
}

DEFAULT_DOSSIER_EXPIRY_DURATION_DAYS: int = 28

# TODO: remove as this is taken from account now
MAX_DOSSIER_EXPIRY_DURATION_DAYS: int = 365

DEFAULT_VALID_DOSSIER_LANGUAGES = ["De", "En", "Fr", "It"]

DEFAULT_VALID_UI_LANGUAGES = ["de", "en", "fr", "it"]

# file processing work
# Does NOT need to be namespaced with DEPLOYMENT_NAMESPACE
ASYNC_ORIGINAL_FILE_PROCESSOR_WORKER_V1_ROUTING_KEY = os.getenv(
    "ASYNC_ORIGINAL_FILE_PROCESSOR_WORKER_V1_ROUTING_KEY",
    "fileprocessworker.ProcessOriginalFileRequestV1",
)

ASYNC_DOSSIER_EVENTS_WORKER_V1_QUEUE_NAME = os.getenv(
    "DOSSIER_EVENTS_WORKER_QUEUE_NAME",
    f"DossierEvents.{DEPLOYMENT_NAMESPACE}.DossierEventV1",
)

# Does NOT need to be namespaced with DEPLOYMENT_NAMESPACE
ASYNC_MAIL_CONSUMER_QUEUE_NAME = os.getenv(
    "ASYNC_MAIL_CONSUMER_QUEUE_NAME", "DossierBackend.dev.MailConsumer.MailRequestV1"
)

ASYNC_IMAGE_EXPORTER_WORKER_V1_QUEUE_NAME = os.getenv(
    "IMAGE_EXPORTER_WORKER_V1_QUEUE_NAME",
    f"ImageExporter.{DEPLOYMENT_NAMESPACE}.ImageExportRequestV1",
)

ASYNC_DOSSIER_COPY_V1_QUEUE_NAME = os.getenv(
    "DOSSIER_EVENTS_COPY_QUEUE_NAME", f"DossierEvent.{DEPLOYMENT_NAMESPACE}.CopyDossier"
)
ASYNC_DOSSIER_COPY_INTO_EXISTING_DOSSIER_V1_QUEUE_NAME = os.getenv(
    "DOSSIER_EVENTS_COPY_INTO_EXISTING_DOSSIER_QUEUE_NAME",
    f"DossierEvent.{DEPLOYMENT_NAMESPACE}.CopyDossierContentsIntoExistingDossierRequest",
)

# sets set_semantic_document_export_done in postgresql after export
ASYNC_DOSSIER_CONSUMER_SEMANTIC_DOCUMENT_EVENT_MESSAGE_TYPE = os.getenv(
    "DOSSIER_CONSUMER_SEMANTIC_DOCUMENT_EVENT_MESSAGE_TYPE",
    f"SemanticDocumentEvent.{DEPLOYMENT_NAMESPACE}.SemanticDocumentPDFResponseV1",
)

ASYNC_DOSSIER_CONSUMER_DOSSIER_ZIPPER_EVENT_MESSAGE_TYPE = os.getenv(
    "ASYNC_DOSSIER_CONSUMER_DOSSIER_ZIPPER_EVENT_MESSAGE_TYPE",
    f"DossierZipper.{DEPLOYMENT_NAMESPACE}.DossierZipperResponseV1",
)


KEYCLOAK_PUBLIC_KEY = os.getenv("KEYCLOAK_PUBLIC_KEY")
PUBLIC_KEY = (
    b"-----BEGIN PUBLIC KEY-----\n"
    + bytes(str(KEYCLOAK_PUBLIC_KEY), "utf-8")
    + b"\n-----END PUBLIC KEY-----"
)


MANAGEMENT_PUBLIC_KEY = os.getenv("MANAGEMENT_PUBLIC_KEY")
MANAGEMENT_PUBLIC_KEY = (
    b"-----BEGIN PUBLIC KEY-----\n"
    + bytes(str(MANAGEMENT_PUBLIC_KEY), "utf-8")
    + b"\n-----END PUBLIC KEY-----"
)


# TODO: ONLY FOR DEVELOPMENT
HARDCODED_NAME_OF_HYPPODOSSIER_ACCOUNT = "HyppoDossier account"

NAME_OF_ROLE_MANAGER_FROM_JWT = "Dossier-manager"

format_datetime = "%Y-%m-%dT%H:%M:%S.%f%z"

# ZIPPER
default_build_path = Path(__file__).parent.parent / "dossier_zipper/asset/build.tar.gz"
DOCUMENT_BROWSER_BUILD_PATH = os.getenv(
    "DOCUMENT_BROWSER_BUILD_PATH", default=str(default_build_path)
)
test_dossier = Path(__file__).parent.parent / "dossier_zipper/test_dossier_v2"
ASYNC_DOSSIER_ZIPPER_WORKER_ROUTING_KEY = os.getenv(
    "ASYNC_DOSSIER_ZIPPER_WORKER_ROUTING_KEY",
    default=f"DossierZipper.{DEPLOYMENT_NAMESPACE}.DossierZipRequestV1",
)
ASYNC_DOSSIER_SEMANTIC_DOCUMENT_PDF_WORKER_ROUTING_KEY = os.getenv(
    "ASYNC_DOSSIER_SEMANTIC_DOCUMENT_PDF_WORKER_ROUTING_KEY",
    default=f"DossierSemanticDocument.{DEPLOYMENT_NAMESPACE}.DossierSemanticDocumentPDFRequestV1",
)


ASYNC_DOSSIER_SEMANTIC_DOCUMENT_PDF_WORKER_V2_ROUTING_KEY = os.getenv(
    "ASYNC_DOSSIER_SEMANTIC_DOCUMENT_PDF_WORKER_V2_ROUTING_KEY",
    default=f"dms.SemanticDocument.{DEPLOYMENT_NAMESPACE}.SemanticDocumentPDFRequestV2",
)

ASYNC_DOSSIER_ZIPPER_WORKER_V2_ROUTING_KEY = os.getenv(
    "ASYNC_DOSSIER_ZIPPER_WORKER_V2_ROUTING_KEY",
    default=f"DossierZipper.{DEPLOYMENT_NAMESPACE}.DossierZipRequestV2",
)


KEYCLOAK_TOKEN_URL = os.getenv("KEYCLOAK_TOKEN_URL")
KEYCLOAK_CLIENT_ID = os.getenv("KEYCLOAK_CLIENT_ID")
KEYCLOAK_CLIENT_SECRET = os.getenv("KEYCLOAK_CLIENT_SECRET")
CLASSIFIER_ENDPOINT = os.getenv("CLASSIFIER_ENDPOINT")

BEKB_SHARED_SECRET = os.getenv("BEKB_SHARED_SECRET", "tqjJnGUQWY4u4vTd5oJY")
SWISSFEX_SHARED_SECRET = os.getenv("SWISSFEX_SHARED_SECRET", "SWISSFEX_SHARED_SECRET")

BEKB_API_ROLE = os.getenv("BEKB_API_ROLE", "bekb_api")
API_ROLE = os.getenv("API_ROLE", "api_role")

ENABLE_BEKB_ARCHIVE_GROUPING_BY_DOCUMENT_CATEGORY = strtobool(
    os.environ.get("ENABLE_BEKB_ARCHIVE_GROUPING_BY_DOCUMENT_CATEGORY", default="False")
)

AUTH_EMAIL_APP_PASSWORD = os.environ.get("AUTH_EMAIL_APP_PASSWORD", default="")

PRETTY_PRINT_LOG = strtobool(os.getenv("PRETTY_PRINT_LOG", "True"))
LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "json_formatter": {
            "()": structlog.stdlib.ProcessorFormatter,
            "processor": structlog.processors.JSONRenderer(),
            "foreign_pre_chain": [
                structlog.contextvars.merge_contextvars,
                structlog.stdlib.add_logger_name,
                structlog.stdlib.add_log_level,
                structlog.stdlib.PositionalArgumentsFormatter(),
            ],
        },
        "plain_console": {
            "()": structlog.stdlib.ProcessorFormatter,
            "processor": structlog.dev.ConsoleRenderer(
                exception_formatter=RichTracebackFormatter(width=-1)
            ),
            "foreign_pre_chain": [
                structlog.contextvars.merge_contextvars,
                structlog.processors.TimeStamper(fmt="iso"),
                structlog.stdlib.add_log_level,
                structlog.stdlib.PositionalArgumentsFormatter(),
                structlog.stdlib.add_logger_name,
            ],
        },
    },
    "handlers": {
        # Important notes regarding handlers.
        #
        # 1. Make sure you use handlers adapted for your project.
        # These handlers configurations are only examples for this library.
        # See python's logging.handlers: https://docs.python.org/3/library/logging.handlers.html
        #
        # 2. You might also want to use different logging configurations depending of the environment.
        # Different files (local.py, test_api.py, production.py, ci.py, etc.) or only conditions.
        # See https://docs.djangoproject.com/en/dev/topics/settings/#designating-the-settings
        "console": {
            "class": "logging.StreamHandler",
            "formatter": "plain_console",
        },
    },
    "loggers": {
        "root": {
            "handlers": ["console"],
            "level": "INFO",
            "propagate": False,
        },
        "django_structlog": {
            "handlers": ["console"],
            "level": "INFO",
            "propagate": False,
        },
        "django": {"handlers": ["console"], "level": "INFO", "propagate": False},
        "django.channels.server": {
            "handlers": ["console"],
            "level": "WARNING",
            "propagate": False,
        },
    },
}

structlog.configure(
    processors=[
        structlog.contextvars.merge_contextvars,
        structlog.stdlib.filter_by_level,
        *([structlog.processors.TimeStamper(fmt="iso")] if PRETTY_PRINT_LOG else []),
        structlog.processors.dict_tracebacks,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.UnicodeDecoder(),
        structlog.stdlib.ProcessorFormatter.wrap_for_formatter,
    ],
    logger_factory=structlog.stdlib.LoggerFactory(),
    cache_logger_on_first_use=True,
)


# Number of messages to be prefetched for event consuming
DOSSIER_EVENT_CONSUMER_PREFETCH = int(
    os.getenv("DOSSIER_EVENT_CONSUMER_PREFETCH", default=20)
)

# Number of parallel processes to upload files to S3
DOSSIER_EVENT_CONSUMER_THREAD_POOL_FILE_UPLOAD = int(
    os.getenv("DOSSIER_EVENT_CONSUMER_THREAD_POOL_FILE_UPLOAD", 10)
)

# Max number of concurrent sessions for file upload
DOSSIER_EVENT_CONSUMER_SESSION_POOL_MAXSIZE = int(
    os.getenv("DOSSIER_EVENT_CONSUMER_SESSION_POOL_MAXSIZE", 80)
)


# The number of tries for the finalize process until backoff gives up and sends the message to dlx queue
DOSSIER_EVENT_CONSUMER_FINALIZE_PROCESS_BACKOFF_MAX_TRIES = int(
    os.getenv("DOSSIER_EVENT_CONSUMER_SESSION_POOL_MAXSIZE", 6)
)


# Postgres path, useful for setting path if not set in shell path (e.g. on mac)
if sys.platform.startswith("darwin"):
    POSTGRES_PATH = os.getenv("POSTGRES_PATH", "")
else:
    POSTGRES_PATH = ""


if DEBUG:
    INSTALLED_APPS += ["django_extensions"]

TEST = strtobool(os.getenv("TEST", "False"))

# Flag to decide if error details including stacktraces should be delivered by the backend via api
# This is disabled as of 6.7.23 due to pentest feedback
# Visibility of column on frontend can be configured in account.enable_error_detail
# To reactivate it would be necessary to check the privilege of the logged in user to decide if this sensitive information
# should be delivered to the frontend.
ENABLE_ERROR_DETAIL_IN_BACKEND = False

# We load private public keypairs from a json file formatted in JWK format
JWK: Optional[JWKPairList] = load_jwk_from_env(
    jwk_path=os.environ.get(
        "JWK_PATH", os.path.join(BASE_DIR, "jwks-example-public.json")
    )
)


API_ACCOUNT_KEY_ZKB = os.getenv("API_ACCOUNT_KEY_ZKB", "zkb")

# URL used for external authorization service used by ZKB
# We call this service to check if a user is allowed access to a dossier
USER_AUTHORIZATION_ENDPOINT_ZKB = os.getenv("USER_AUTHORIZATION_ENDPOINT_ZKB", "")

# Time in minutes to check for unprocessed files
FILE_STATUS_MAX_PROCESSING_TIME = int(
    os.getenv("FILE_STATUS_MAX_PROCESSING_TIME", default=15)
)
FILE_STATUS_CHECK_INTERVAL = int(os.getenv("FILE_STATUS_CHECK_INTERVAL", default=5))


# ZKB specific settings for the access to the authorization service
ZKB_SOCKS5_PROXY = os.getenv("ZKB_SOCKS5_PROXY")
ZKB_AUTHORIZATION_ENDPOINT = os.getenv("ZKB_AUTHORIZATION_ENDPOINT")
ZKB_AUTHORIZATION_CERT_PEM_FILE = os.getenv("ZKB_AUTHORIZATION_CERT_PEM_FILE")
ZKB_AUTHORIZATION_CERT_KEY_FILE = os.getenv("ZKB_AUTHORIZATION_CERT_KEY_FILE")


ZKB_AUTHORIZATION_ENDPOINT_APPROVED_TTL_MINUTES = os.getenv(
    "ZKB_AUTHORIZATION_ENDPOINT_APPROVED_TTL_MINUTES", 10
)
ZKB_AUTHORIZATION_ENDPOINT_DENIED_TTL_MINUTES = os.getenv(
    "ZKB_AUTHORIZATION_ENDPOINT_DENIED_TTL_MINUTES", 5
)

# Default is False, set actively to True for ZKBx accounts
ZKB_VBV_USE_EXTRACTED_FILE = strtobool(
    os.environ.get("ZKB_VBV_USE_EXTRACTED_FILE", default="False")
)

# This doccat gets a special treatment
ZKB_VBV_DOCUMENT_CATEGORY_KEY: str = "ZKB_VBV"


TEMPFILESTORE_S3_ENDPOINT = os.getenv("TEMPFILESTORE_S3_ENDPOINT", "sos-ch-dk-2.exo.io")
TEMPFILESTORE_S3_ACCESS_KEY = os.getenv("TEMPFILESTORE_S3_ACCESS_KEY")
TEMPFILESTORE_S3_SECRET_KEY = os.getenv("TEMPFILESTORE_S3_SECRET_KEY")
TEMPFILESTORE_S3_SECURE = strtobool(os.getenv("TEMPFILESTORE_S3_SECURE", "True"))
TEMPFILESTORE_S3_REGION = os.getenv("TEMPFILESTORE_S3_REGION", "ch-dk-2")
TEMPFILESTORE_S3_BUCKET = os.getenv("TEMPFILESTORE_S3_BUCKET", "dev-tempfilestore")

# Bool to describe whether running in CI or not
# Currently doesn't work as CI doesn't appear to be passed into docker into docker config
# in gitlab.
CI = strtobool(os.getenv("CI", "False"))

# Default is False, set actively to True to dynamically add new document categories
ADD_NEW_DOCUMENT_CATEGORIES_FROM_PROCESSING = strtobool(
    os.environ.get("ADD_NEW_DOCUMENT_CATEGORIES_FROM_PROCESSING", default="False")
)

ENABLE_PAGE_OBJECTS_IN_DATA_V2 = strtobool(
    os.environ.get("ENABLE_PAGE_OBJECTS_IN_DATA_V2", default="True")
)

# private jwk for signing tokens
INSTANCE_SIGNING_KEY = os.getenv("INSTANCE_SIGNING_KEY")

# global confidence threshold for cdp recommendations
CDP_CONFIDENCE_THRESHOLD = float(os.getenv("CDP_CONFIDENCE_THRESHOLD", "0.7"))

# fuzzy text matching score threshold for field context
CDP_FIELD_CONTEXT_MATCH_THRESHOLD = float(
    os.getenv("CDP_FIELD_CONTEXT_MATCH_THRESHOLD", "90.0")
)

HYPYFREP_PDFA_CONVERT_REQUEST_ROUTING_KEY = os.getenv(
    "HYPYFREP_PDFA_CONVERT_REQUEST_ROUTING_KEY",
    default="Hypyfrep.PDFA.Convert.Request.RoutingKey",
)

# How many times to retry rpc call for pdfa conversion
HYPYFREP_PDFA_CONVERT_RETRY_COUNT = int(
    os.getenv("HYPYFREP_PDFA_CONVERT_RETRY_COUNT", 5)
)

# This is the accepted max length of suffix. Currently fixed to 170. Is used in SwissFEX schema so it should not
# be overridden without checking the consequences.
# Constant exists so it can be used everywhere where the limitation is applied / important.
MAX_LENGTH_SEMANTIC_DOCUMENT_TITLE_SUFFIX: int = 170

# Tailwind CSS configuration
TAILWIND_APP_NAME = "theme"
