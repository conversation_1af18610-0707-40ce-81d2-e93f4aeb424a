{"key": "bekbz", "description": "Standard Regelwerk für die BEKB", "business_case_types": [{"key": "UNKNOWN", "name_de": "unbekannt", "description_de": "", "name_en": "unknown", "description_en": "", "name_fr": "inconnu", "description_fr": "", "name_it": "scon<PERSON><PERSON><PERSON>", "description_it": "", "order": 2}, {"key": "NEW_PURCHASE", "name_de": "Neugeschäft, Kauf", "description_de": null, "name_en": "New business, purchase", "description_en": null, "name_fr": "Nouvelle affaire, achat", "description_fr": null, "name_it": "Nuova attività, acquisto", "description_it": null, "order": 2}, {"key": "NEW_REFINANCE", "name_de": "Neugeschäft, Ablösung", "description_de": null, "name_en": "New business, refinance", "description_en": null, "name_fr": "Nouvelle affaire, refinancement", "description_fr": null, "name_it": "Nuove attività, rifinanziamento", "description_it": null, "order": 3}, {"key": "DEBTOR_CHANGE", "name_de": "Schuldnerwechsel", "description_de": null, "name_en": "Change of debtor", "description_en": null, "name_fr": "Changement de débiteur", "description_fr": null, "name_it": "Cambio di debitore", "description_it": null, "order": 4}, {"key": "INCREASE", "name_de": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description_de": null, "name_en": "Increase", "description_en": null, "name_fr": "Augmentation", "description_fr": null, "name_it": "Aumento", "description_it": null, "order": 5}, {"key": "PROLONGATION", "name_de": "Verlängerung", "description_de": null, "name_en": "Prolongation", "description_en": null, "name_fr": "Prolongation", "description_fr": null, "name_it": "Estensione", "description_it": null, "order": 6}, {"key": "SCANNING", "name_de": "<PERSON><PERSON><PERSON>", "description_de": null, "name_en": "General scanning", "description_en": null, "name_fr": "Scanning général", "description_fr": null, "name_it": "Scansione generale", "description_it": null, "order": 7}, {"key": "MUTATION", "name_de": "Mutation", "description_de": null, "name_en": "Mutation", "description_en": null, "name_fr": "Mutation", "description_fr": null, "name_it": "Mutazione", "description_it": null, "order": 7}, {"key": "ACCOUNT_CANCELLATION", "name_de": "Saldierung", "description_de": null, "name_en": "Account cancellation", "description_en": null, "name_fr": "Clôture des comptes", "description_fr": null, "name_it": "Bilanciamento del conto", "description_it": null, "order": 8}], "fields": [{"entity": 2, "key": "main_income", "name_de": "<PERSON><PERSON><PERSON>kommen", "name_en": "Main income", "name_fr": "Revenu principal", "name_it": "Reddito principale", "type": 1, "choices": [{"key": "employed", "name_de": "angestellt", "name_en": "employed", "name_fr": "employé(e)", "name_it": "impiegato", "order": 26}, {"key": "self", "name_de": "selbständig", "name_en": "self-employed", "name_fr": "indépendant", "name_it": "lavoratore autonomo", "order": 27}, {"key": "retired", "name_de": "in Rente", "name_en": "retired", "name_fr": "à la retraite", "name_it": "in pensione", "order": 28}, {"key": "no", "name_de": "kein <PERSON>", "name_en": "no income", "name_fr": "pas de revenu", "name_it": "nessun reddito", "order": 29}], "choices_default": "employed", "shown_for_the_following_business_case_types": ["NEW_PURCHASE", "NEW_REFINANCE", "DEBTOR_CHANGE", "INCREASE"]}, {"entity": 2, "key": "pledge_pillar_3_account", "name_de": "Verpfändung Säule 3 Konto", "name_en": "Pledge pillar 3 account", "name_fr": "Mise en gage du compte du pilier 3", "name_it": "Conto corrente del pilastro 3", "type": 2, "choices": [], "choices_default": null, "shown_for_the_following_business_case_types": ["NEW_PURCHASE", "NEW_REFINANCE", "DEBTOR_CHANGE", "INCREASE"]}, {"entity": 2, "key": "pledge_pillar_3_insurance", "name_de": "Verpfändung Säule 3 Police", "name_en": "Pledge pillar 3 policy", "name_fr": "Mise en gage de la police du pilier 3", "name_it": "Politica del pilastro 3 promessa", "type": 2, "choices": [], "choices_default": null, "shown_for_the_following_business_case_types": ["NEW_PURCHASE", "NEW_REFINANCE", "DEBTOR_CHANGE", "INCREASE"]}, {"entity": 2, "key": "has_divorce", "name_de": "Scheidung (sofern relevant)", "name_en": "Divorce (if relevant)", "name_fr": "Divorce (si pertinent)", "name_it": "Divorzio (se pertinente)", "type": 2, "choices": [], "choices_default": null, "shown_for_the_following_business_case_types": ["NEW_PURCHASE", "NEW_REFINANCE", "DEBTOR_CHANGE", "INCREASE"]}, {"entity": 2, "key": "has_liabilities", "name_de": "Leasing oder Konsumkredit", "name_en": "Leasing or consumer credit", "name_fr": "Leasing ou crédit à la consommation", "name_it": "Leasing o credito al consumo", "type": 2, "choices": [], "choices_default": null, "shown_for_the_following_business_case_types": ["NEW_PURCHASE", "NEW_REFINANCE", "DEBTOR_CHANGE", "INCREASE"]}, {"entity": 3, "key": "type", "name_de": "Liegenschaftstyp", "name_en": "Property type", "name_fr": "Type de bien immobilier", "name_it": "Tipo di proprietà", "type": 1, "choices": [{"key": "STWE", "name_de": "Eigentumswohnung", "name_en": "Condominium", "name_fr": "Appartement en copropriété", "name_it": "Condominio", "order": 30}, {"key": "EFH", "name_de": "Einfamilienhaus", "name_en": "Single family house", "name_fr": "<PERSON><PERSON> individ<PERSON>", "name_it": "Casa unifamiliare", "order": 31}, {"key": "MFH", "name_de": "Mehrfamilienhaus", "name_en": "Apartment house", "name_fr": "Immeuble collectif", "name_it": "Casa appartamento", "order": 32}, {"key": "other", "name_de": "andere", "name_en": "other", "name_fr": "autre", "name_it": "altro", "order": 33}], "choices_default": "STWE", "shown_for_the_following_business_case_types": ["NEW_PURCHASE", "NEW_REFINANCE", "DEBTOR_CHANGE", "INCREASE"]}, {"entity": 3, "key": "address_canton", "name_de": "<PERSON><PERSON>", "name_en": "Canton", "name_fr": "Canton", "name_it": "Canton", "type": 1, "choices": [{"key": "AI", "name_de": "AI", "name_en": "AI", "name_fr": "AI", "name_it": "AI", "order": 1}, {"key": "AR", "name_de": "AR", "name_en": "AR", "name_fr": "AR", "name_it": "AR", "order": 2}, {"key": "BE", "name_de": "BE", "name_en": "BE", "name_fr": "BE", "name_it": "BE", "order": 3}, {"key": "BL", "name_de": "BL", "name_en": "BL", "name_fr": "BL", "name_it": "BL", "order": 4}, {"key": "BS", "name_de": "BS", "name_en": "BS", "name_fr": "BS", "name_it": "BS", "order": 5}, {"key": "FR", "name_de": "FR", "name_en": "FR", "name_fr": "FR", "name_it": "FR", "order": 6}, {"key": "GE", "name_de": "GE", "name_en": "GE", "name_fr": "GE", "name_it": "GE", "order": 7}, {"key": "GL", "name_de": "GL", "name_en": "GL", "name_fr": "GL", "name_it": "GL", "order": 8}, {"key": "GR", "name_de": "GR", "name_en": "GR", "name_fr": "GR", "name_it": "GR", "order": 9}, {"key": "JU", "name_de": "JU", "name_en": "JU", "name_fr": "JU", "name_it": "JU", "order": 10}, {"key": "LU", "name_de": "LU", "name_en": "LU", "name_fr": "LU", "name_it": "LU", "order": 11}, {"key": "NE", "name_de": "NE", "name_en": "NE", "name_fr": "NE", "name_it": "NE", "order": 12}, {"key": "NW", "name_de": "NW", "name_en": "NW", "name_fr": "NW", "name_it": "NW", "order": 13}, {"key": "OW", "name_de": "OW", "name_en": "OW", "name_fr": "OW", "name_it": "OW", "order": 14}, {"key": "SH", "name_de": "SH", "name_en": "SH", "name_fr": "SH", "name_it": "SH", "order": 15}, {"key": "SO", "name_de": "SO", "name_en": "SO", "name_fr": "SO", "name_it": "SO", "order": 16}, {"key": "TG", "name_de": "TG", "name_en": "TG", "name_fr": "TG", "name_it": "TG", "order": 17}, {"key": "SG", "name_de": "SG", "name_en": "SG", "name_fr": "SG", "name_it": "SG", "order": 18}, {"key": "SZ", "name_de": "SZ", "name_en": "SZ", "name_fr": "SZ", "name_it": "SZ", "order": 19}, {"key": "TI", "name_de": "TI", "name_en": "TI", "name_fr": "TI", "name_it": "TI", "order": 20}, {"key": "UR", "name_de": "UR", "name_en": "UR", "name_fr": "UR", "name_it": "UR", "order": 21}, {"key": "VD", "name_de": "VD", "name_en": "VD", "name_fr": "VD", "name_it": "VD", "order": 22}, {"key": "VS", "name_de": "VS", "name_en": "VS", "name_fr": "VS", "name_it": "VS", "order": 23}, {"key": "ZG", "name_de": "ZG", "name_en": "ZG", "name_fr": "ZG", "name_it": "ZG", "order": 24}, {"key": "ZH", "name_de": "ZH", "name_en": "ZH", "name_fr": "ZH", "name_it": "ZH", "order": 25}, {"key": "AG", "name_de": "AG", "name_en": "AG", "name_fr": "AG", "name_it": "AG", "order": 34}], "choices_default": "BE", "shown_for_the_following_business_case_types": ["NEW_PURCHASE", "NEW_REFINANCE", "DEBTOR_CHANGE", "INCREASE"]}, {"entity": 3, "key": "for_rent", "name_de": "Vermietet", "name_en": "Rented out", "name_fr": "<PERSON><PERSON>", "name_it": "Affittato", "type": 2, "choices": [], "choices_default": null, "shown_for_the_following_business_case_types": ["NEW_PURCHASE", "NEW_REFINANCE", "DEBTOR_CHANGE", "INCREASE"]}, {"entity": 3, "key": "purchase_year_lt_2y", "name_de": "<PERSON><PERSON> vor weniger als 2 Jahren", "name_en": "Purchase less than 2 years ago", "name_fr": "Date d'achat inférieure à 2 ans", "name_it": "Data di acquisto inferiore a 2 anni", "type": 2, "choices": [], "choices_default": null, "shown_for_the_following_business_case_types": ["NEW_REFINANCE"]}, {"entity": 3, "key": "has_use_restrictions", "name_de": "Baurecht", "name_en": "Building lease", "name_fr": "Droit de superficie", "name_it": "Locazione di edifici", "type": 2, "choices": [], "choices_default": null, "shown_for_the_following_business_case_types": ["NEW_PURCHASE", "NEW_REFINANCE", "DEBTOR_CHANGE", "INCREASE"]}, {"entity": 3, "key": "has_reconstructions", "name_de": "Umbau / Renovation", "name_en": "Conversion / renovation", "name_fr": "Transformation / rénovation", "name_it": "Conversione / ristrutturazione", "type": 2, "choices": [], "choices_default": null, "shown_for_the_following_business_case_types": ["NEW_PURCHASE", "NEW_REFINANCE", "DEBTOR_CHANGE", "INCREASE"]}, {"entity": 3, "key": "has_construction_contract", "name_de": "<PERSON><PERSON><PERSON><PERSON><PERSON> (GU) / <PERSON><PERSON><PERSON><PERSON><PERSON> (TU)", "name_en": "General contractor / total contractor", "name_fr": "Entrepreneur général / entrepreneur total", "name_it": "Impresa generale / impresa totale", "type": 2, "choices": [], "choices_default": null, "shown_for_the_following_business_case_types": ["NEW_PURCHASE"]}], "completeness_rules": [{"key": "tax_decl", "title_de": "Steuererklärung", "title_en": "Tax declaration", "title_fr": "Déclar<PERSON> d'impôts", "title_it": "Dichiarazione d'imposta", "desc_de": "aktuellste, max. 2 Jahre alt", "desc_en": "most recent, max. 2 years old", "desc_fr": "le plus récent, datant de 2 ans au maximum", "desc_it": "pi<PERSON> recente, massimo 2 anni", "entity": 2, "field": null, "values": null, "strictness": "required", "order": 1, "business_case_types": ["NEW_PURCHASE", "NEW_REFINANCE", "DEBTOR_CHANGE", "INCREASE"], "document_options": [{"document_category": {"name": "TAX_DECLARATION"}, "max_doc_age_in_months": 24}]}, {"key": "pension", "title_de": "Pensionskassenausweis", "title_en": "Pension Certificate", "title_fr": "Certificat de prévoyance", "title_it": "Certificato della cassa pensione", "desc_de": "alternativ: Auszug eines Freizügigkeitskonto, max. 1 Jahr alt", "desc_en": "alternatively: statement of a vested benefits account, max. 1 year old", "desc_fr": "alternative : extrait d'un compte de libre passage, datant de max. 1 an", "desc_it": "in alternativa: estratto conto di un conto di libero passaggio, al massimo di 1 anno", "entity": 2, "field": null, "values": null, "strictness": "required", "order": 2, "business_case_types": ["NEW_PURCHASE", "NEW_REFINANCE", "DEBTOR_CHANGE", "INCREASE"], "document_options": [{"document_category": {"name": "VESTED_BENEFITS_STATEMENT"}, "max_doc_age_in_months": 12}, {"document_category": {"name": "VESTED_BENEFITS_ACCOUNT"}, "max_doc_age_in_months": 12}, {"document_category": {"name": "STATEMENT_PENSION"}, "max_doc_age_in_months": 12}, {"document_category": {"name": "PENSION_CERTIFICATE"}, "max_doc_age_in_months": 12}]}, {"key": "assets", "title_de": "Eigenmittelnachweis", "title_en": "Proof of Funds for Down Payment", "title_fr": "Fonds propres", "title_it": "Prove del capitale proprio", "desc_de": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, PK-Bezug (max 1 Jahr alt); Kontoauszug (max. 3 Monate alt)", "desc_en": "Gift, advance withdrawal of inheritance, loan, pension fund withdrawal (max. 1 year old); account statement (max. 3 months old)", "desc_fr": "Donation, avance d'hoirie, prêt, retrait CP (datant de max. 1 an) ; relevé de compte (datant de max. 3 mois)", "desc_it": "<PERSON><PERSON>, prelievi anticipati di eredità, prestiti, prelievi di fondi pensione (max. 1 anno); est<PERSON>to conto (max. 3 mesi)", "entity": 2, "field": null, "values": null, "strictness": "required", "order": 3, "business_case_types": ["NEW_PURCHASE"], "document_options": [{"document_category": {"name": "PROOF_OF_FUNDS"}, "max_doc_age_in_months": 12}, {"document_category": {"name": "BANK_DOCUMENT"}, "max_doc_age_in_months": null}]}, {"key": "divorce", "title_de": "Scheidungskonvention", "title_en": "Divorce Convention", "title_fr": "Convention de divorce", "title_it": "Convenzione di divorzio", "desc_de": "", "desc_en": "", "desc_fr": "", "desc_it": "", "entity": 2, "field": "has_divorce", "values": "True", "strictness": "required", "order": 4, "business_case_types": ["NEW_PURCHASE", "NEW_REFINANCE", "DEBTOR_CHANGE"], "document_options": [{"document_category": {"name": "DIVORCE_CONVENTION"}, "max_doc_age_in_months": null}, {"document_category": {"name": "DIVORCE_DECREE"}, "max_doc_age_in_months": null}, {"document_category": {"name": "DIVORCE_DOCUMENT"}, "max_doc_age_in_months": null}, {"document_category": {"name": "DIVORCE_SEPARATION_AGREEMENT"}, "max_doc_age_in_months": null}]}, {"key": "income_employed_1y", "title_de": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "title_en": "Salary Certificate", "title_fr": "Certificat de salaire", "title_it": "Certificato di salario", "desc_de": "Optionen: <PERSON><PERSON><PERSON><PERSON><PERSON> (max. 1 Jahr alt), <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (max. 3 Monate alt), Arbeitsbestätigung (max. 3 Monate alt); Lohneingang auf BEKB-Konto (via \"Dokumentenzuweisung umgehen\" mit Kommentar erfassen)", "desc_en": "Options: Wage statement (max. 1 year old), salary statement (max. 3 months old), confirmation of employment (max. 3 months old); enter incoming wages on BEKB account (via \"Bypass document assignment\" with comment)", "desc_fr": "Options: certificat de salaire (datant de max. 1 an), attestation de travail (datant de max. 3 mois); saisir l'entrée de salaire sur le compte BEKB (via \"Contourner l'attribution de document\" avec un commentaire)", "desc_it": "Opzioni: Dichiarazione di salario (max. 1 anno), dichiarazione di salario (max. 3 mesi), conferma di assunzione (max. 3 mesi); inserimento dei salari in entrata sul conto BEKB (tramite \"Bypass assegnazione documenti\" con commenti)", "entity": 2, "field": "get_main_income_key", "values": "employed", "strictness": "required", "order": 5, "business_case_types": ["NEW_PURCHASE", "NEW_REFINANCE", "DEBTOR_CHANGE"], "document_options": [{"document_category": {"name": "SALARY_CERTIFICATE"}, "max_doc_age_in_months": 12}, {"document_category": {"name": "EMPLOYMENT_CONFIRMATION"}, "max_doc_age_in_months": 3}, {"document_category": {"name": "SALARY_CONFIRMATION_FORM"}, "max_doc_age_in_months": 3}, {"document_category": {"name": "PAYSLIP"}, "max_doc_age_in_months": 3}, {"document_category": {"name": "SALARY_ACCOUNT"}, "max_doc_age_in_months": 3}, {"document_category": {"name": "SALARY_CONFIRMATION"}, "max_doc_age_in_months": 3}]}, {"key": "income_employed_3y", "title_de": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "title_en": "Salary Certificate", "title_fr": "Certificat de salaire", "title_it": "Certificato di salario", "desc_de": "max. 3 Jahre alt", "desc_en": "max. 3 years old", "desc_fr": "datant de max. 3 ans", "desc_it": "max. 3 anni", "entity": 2, "field": "get_main_income_key", "values": "employed", "strictness": "required", "order": 6, "business_case_types": ["INCREASE"], "document_options": [{"document_category": {"name": "SALARY_CERTIFICATE"}, "max_doc_age_in_months": 36}]}, {"key": "income_self", "title_de": "Jahresrechnung Firma", "title_en": "Annual Financial Statement of Company", "title_fr": "État financier annuel de la société", "title_it": "Bilancio annuale della società", "desc_de": "möglichst über die letzten drei Jahre", "desc_en": "if possible over the last three years", "desc_fr": "si possible sur les trois dernières années", "desc_it": "se possibile negli ultimi tre anni", "entity": 2, "field": "get_main_income_key", "values": "self", "strictness": "required", "order": 7, "business_case_types": ["NEW_PURCHASE", "NEW_REFINANCE", "DEBTOR_CHANGE", "INCREASE"], "document_options": [{"document_category": {"name": "FINANCIAL_STATEMENT_COMPANY"}, "max_doc_age_in_months": 36}]}, {"key": "income_ahv_1y", "title_de": "Rentenbescheinigung AHV", "title_en": "Pension Attestation AHV", "title_fr": "Rente AVS", "title_it": "Pensione AVS", "desc_de": "max. 1 Jahr alt", "desc_en": "max. 1 year old", "desc_fr": "vieux de max. 1 an", "desc_it": "max. 1 anno", "entity": 2, "field": "get_main_income_key", "values": "retired", "strictness": "required", "order": 8, "business_case_types": ["NEW_PURCHASE", "NEW_REFINANCE", "DEBTOR_CHANGE"], "document_options": [{"document_category": {"name": "PENSION_PAYMENT_AHV"}, "max_doc_age_in_months": 12}]}, {"key": "income_ahv_3y", "title_de": "Rentenbescheinigung AHV", "title_en": "Pension Attestation AHV", "title_fr": "Rente AVS", "title_it": "Pensione AVS", "desc_de": "max. 3 Jahre alt", "desc_en": "max. 3 years old", "desc_fr": "vieux de 3 ans max.", "desc_it": "max. 3 anni", "entity": 2, "field": "get_main_income_key", "values": "retired", "strictness": "required", "order": 9, "business_case_types": ["INCREASE"], "document_options": [{"document_category": {"name": "PENSION_PAYMENT_AHV"}, "max_doc_age_in_months": 36}]}, {"key": "income_pk_1y", "title_de": "Rentenbescheinigung PK", "title_en": "Pension Attestation", "title_fr": "Attestation de rente", "title_it": "Attestazione della pensione", "desc_de": "max. 1 Jahr alt", "desc_en": "max. 1 year old", "desc_fr": "max. 1 an d'âge", "desc_it": "max. 1 anno", "entity": 2, "field": "get_main_income_key", "values": "retired", "strictness": "required", "order": 10, "business_case_types": ["NEW_PURCHASE", "NEW_REFINANCE", "DEBTOR_CHANGE"], "document_options": [{"document_category": {"name": "PENSION_PAYMENT_BVG"}, "max_doc_age_in_months": 12}]}, {"key": "income_pk_3y", "title_de": "Rentenbescheinigung PK", "title_en": "Pension Attestation", "title_fr": "Attestation de rente", "title_it": "Attestazione della pensione", "desc_de": "max. 3 Jahre alt", "desc_en": "max. 3 years old", "desc_fr": "max. 3 ans d'âge", "desc_it": "max. 3 anni", "entity": 2, "field": "get_main_income_key", "values": "retired", "strictness": "required", "order": 11, "business_case_types": ["INCREASE"], "document_options": [{"document_category": {"name": "PENSION_PAYMENT_BVG"}, "max_doc_age_in_months": 36}]}, {"key": "pledge_pillar_3_account_1y", "title_de": "Vorsogekonto 3a", "title_en": "Retirement account 3a", "title_fr": "Compte de prévoyance 3a", "title_it": "Conto di risparmio per la pensione 3a", "desc_de": "max. 1 Jahr alt", "desc_en": "max. 1 year old", "desc_fr": "max. 1 an d'âge", "desc_it": "max. 1 anno", "entity": 2, "field": "pledge_pillar_3_account", "values": "True", "strictness": "required", "order": 12, "business_case_types": ["NEW_PURCHASE", "NEW_REFINANCE"], "document_options": [{"document_category": {"name": "PENSION3A_ACCOUNT"}, "max_doc_age_in_months": 12}]}, {"key": "pledge_pillar_3_account_2y", "title_de": "Vorsogekonto 3a", "title_en": "Retirement account 3a", "title_fr": "Compte de prévoyance 3a", "title_it": "Conto di risparmio per la pensione 3a", "desc_de": "max. 2 Jahre alt", "desc_en": "max. 2 years old", "desc_fr": "max. 2 ans d'âge", "desc_it": "max. 2 anni", "entity": 2, "field": "pledge_pillar_3_account", "values": "True", "strictness": "required", "order": 13, "business_case_types": ["DEBTOR_CHANGE", "INCREASE"], "document_options": [{"document_category": {"name": "PENSION3A_ACCOUNT"}, "max_doc_age_in_months": 24}]}, {"key": "pledge_pillar_3_insurance_contract_1y", "title_de": "Vorsorgepolice Säule 3", "title_en": "Pillar 3 Insurance Policy", "title_fr": "Police de prévoyance 3ème pilier", "title_it": "Polizza di previdenza 3", "desc_de": "", "desc_en": "", "desc_fr": "", "desc_it": "", "entity": 2, "field": "pledge_pillar_3_insurance", "values": "True", "strictness": "required", "order": 14, "business_case_types": ["NEW_PURCHASE", "NEW_REFINANCE"], "document_options": [{"document_category": {"name": "PENSION3A_INSURANCE_CONTRACT"}, "max_doc_age_in_months": null}]}, {"key": "pledge_pillar_3_insurance_contract_2y", "title_de": "Vorsorgepolice Säule 3", "title_en": "Pillar 3 Insurance Policy", "title_fr": "Police de prévoyance 3ème pilier", "title_it": "Polizza di previdenza 3", "desc_de": "", "desc_en": "", "desc_fr": "", "desc_it": "", "entity": 2, "field": "pledge_pillar_3_insurance", "values": "True", "strictness": "required", "order": 15, "business_case_types": ["DEBTOR_CHANGE", "INCREASE"], "document_options": [{"document_category": {"name": "PENSION3A_INSURANCE_CONTRACT"}, "max_doc_age_in_months": null}]}, {"key": "pledge_pillar_3_insurance_1y", "title_de": "Rückkaufswert Police", "title_en": "Redemption value policy", "title_fr": "Valeur de rachat de la police", "title_it": "Valore di riscatto del polizza", "desc_de": "max. 1 Jahr alt; falls der aktuelle Rückkaufswert explizit in der Versicherungspolice ausgewiesen wird, bitte “Dokumentenzuweisung umgehen” und als Kommentar vermerken", "desc_en": "max. 1 year old; if the current surrender value is explicitly stated in the insurance policy, please “Bypass document allocation” and note this as a comment", "desc_fr": "max. 1 an d'âge; si la valeur de rachat actuelle est explicitement indiquée dans la police d'assurance, veuil<PERSON>z « contourner l'attribution de document » et la noter comme commentaire", "desc_it": "max. 1 anno; se il valore di riscatto attuale è esplicitamente indicato nella polizza assicurativa, si prega di “bypassare l'assegnazione del documento” e di annotarlo come commento", "entity": 2, "field": "pledge_pillar_3_insurance", "values": "True", "strictness": "required", "order": 16, "business_case_types": ["NEW_PURCHASE", "NEW_REFINANCE"], "document_options": [{"document_category": {"name": "PENSION3A_INSURANCE_LETTER_REDEMPTION"}, "max_doc_age_in_months": 12}]}, {"key": "pledge_pillar_3_insurance_2y", "title_de": "Rückkaufswert Police", "title_en": "Redemption value policy", "title_fr": "Valeur de rachat de la police", "title_it": "Valore di riscatto del polizza", "desc_de": "max. 2 Jahre alt; falls der aktuelle Rückkaufswert explizit in der Versicherungspolice ausgewiesen wird, bitte “Dokumentenzuweisung umgehen” und als Kommentar vermerken", "desc_en": "max. 2 years old; if the current surrender value is explicitly stated in the insurance policy, please “Bypass document allocation” and note this as a comment", "desc_fr": "max. 2 ans d'âge; si la valeur de rachat actuelle est explicitement indiquée dans la police d'assurance, veuil<PERSON><PERSON> « contourner l'attribution de document » et la noter comme commentaire", "desc_it": "max. 2 anni;  se il valore di riscatto attuale è esplicitamente indicato nella polizza assicurativa, si prega di “bypassare l'assegnazione del documento” e di annotarlo come commento", "entity": 2, "field": "pledge_pillar_3_insurance", "values": "True", "strictness": "required", "order": 17, "business_case_types": ["DEBTOR_CHANGE", "INCREASE"], "document_options": [{"document_category": {"name": "PENSION3A_INSURANCE_LETTER_REDEMPTION"}, "max_doc_age_in_months": 24}]}, {"key": "liabilities", "title_de": "Leasing, Konsumkredit", "title_en": "Leasing, consumer loan", "title_fr": "Leasing, crédit à la consommation", "title_it": "Leasing, credito al consumo", "desc_de": "", "desc_en": "", "desc_fr": "", "desc_it": "", "entity": 2, "field": "has_liabilities", "values": "True", "strictness": "required", "order": 18, "business_case_types": ["NEW_PURCHASE", "NEW_REFINANCE", "DEBTOR_CHANGE", "INCREASE"], "document_options": [{"document_category": {"name": "LEASING_AGREEMENT"}, "max_doc_age_in_months": 12}, {"document_category": {"name": "LOAN_AGREEMENT"}, "max_doc_age_in_months": 12}, {"document_category": {"name": "CONSUMER_LOAN"}, "max_doc_age_in_months": null}]}, {"key": "land_register", "title_de": "Grundbuchauszug", "title_en": "Extract from Land Register", "title_fr": "Extrait du registre foncier", "title_it": "Estratto del registro fondiario", "desc_de": "max. 6 Monate alt", "desc_en": "max. 6 months old", "desc_fr": "max. 6 mois d'âge", "desc_it": "max. 6 mesi", "entity": 3, "field": null, "values": null, "strictness": "required", "order": 19, "business_case_types": ["NEW_REFINANCE", "DEBTOR_CHANGE", "INCREASE"], "document_options": [{"document_category": {"name": "EXTRACT_FROM_LAND_REGISTER"}, "max_doc_age_in_months": 6}]}, {"key": "sales_contract", "title_de": "Kaufvertrag", "title_en": "Contract of Sale", "title_fr": "Contrat de vente", "title_it": "<PERSON><PERSON><PERSON> di compravendita", "desc_de": "", "desc_en": "", "desc_fr": "", "desc_it": "", "entity": 3, "field": null, "values": null, "strictness": "required", "order": 20, "business_case_types": ["NEW_PURCHASE"], "document_options": [{"document_category": {"name": "CONTRACT_OF_SALE"}, "max_doc_age_in_months": null}]}, {"key": "sales_contract_2y", "title_de": "Kaufvertrag", "title_en": "Contract of Sale", "title_fr": "Contrat de vente", "title_it": "<PERSON><PERSON><PERSON> di compravendita", "desc_de": "", "desc_en": "", "desc_fr": "", "desc_it": "", "entity": 3, "field": "purchase_year_lt_2y", "values": "True", "strictness": "required", "order": 21, "business_case_types": ["NEW_REFINANCE"], "document_options": [{"document_category": {"name": "CONTRACT_OF_SALE"}, "max_doc_age_in_months": null}]}, {"key": "property_change", "title_de": "Handänderungsvertrag", "title_en": "Contract of change of ownership", "title_fr": "Contrat de changement de propriétaire", "title_it": "Contratto di cambio di proprietà", "desc_de": "", "desc_en": "", "desc_fr": "", "desc_it": "", "entity": 3, "field": null, "values": null, "strictness": "required", "order": 22, "business_case_types": ["DEBTOR_CHANGE"], "document_options": [{"document_category": {"name": "CONTRACT_OF_SALE"}, "max_doc_age_in_months": null}]}, {"key": "sales_documentation", "title_de": "Verkaufsdokumentation", "title_en": "Sales Documentation", "title_fr": "Documentation de vente", "title_it": "Documentazione di vendita", "desc_de": "", "desc_en": "", "desc_fr": "", "desc_it": "", "entity": 3, "field": null, "values": null, "strictness": "required", "order": 23, "business_case_types": ["NEW_PURCHASE"], "document_options": [{"document_category": {"name": "SALES_DOCUMENTATION"}, "max_doc_age_in_months": null}, {"document_category": {"name": "PROPERTY_DOCUMENTATION"}, "max_doc_age_in_months": null}]}, {"key": "floor_plan", "title_de": "Plan, <PERSON><PERSON><PERSON><PERSON>", "title_en": "Plan, floor plan", "title_fr": "Plan, plan de masse", "title_it": "Pianta, planimetria", "desc_de": "", "desc_en": "", "desc_fr": "", "desc_it": "", "entity": 3, "field": null, "values": null, "strictness": "required", "order": 24, "business_case_types": ["NEW_REFINANCE", "DEBTOR_CHANGE"], "document_options": [{"document_category": {"name": "PLAN_FLOOR"}, "max_doc_age_in_months": null}]}, {"key": "photo", "title_de": "Mind. 1 aktuelles Foto", "title_en": "At least 1 recent photo", "title_fr": "Au moins 1 photo récente", "title_it": "Almeno 1 foto recente", "desc_de": "", "desc_en": "", "desc_fr": "", "desc_it": "", "entity": 3, "field": null, "values": null, "strictness": "required", "order": 25, "business_case_types": ["NEW_REFINANCE", "DEBTOR_CHANGE", "INCREASE"], "document_options": [{"document_category": {"name": "PROPERTY_PHOTOS"}, "max_doc_age_in_months": null}]}, {"key": "property_insurance_efh", "title_de": "Gebäudeversicherungsausweis", "title_en": "Insurance Certificate on Building", "title_fr": "Attestation assurance des batiments", "title_it": "Certificato assicurazione dello stabile", "desc_de": "", "desc_en": "", "desc_fr": "", "desc_it": "", "entity": 3, "field": "get_type_key", "values": "EFH", "strictness": "required", "order": 26, "business_case_types": ["NEW_PURCHASE", "NEW_REFINANCE", "DEBTOR_CHANGE"], "document_options": [{"document_category": {"name": "PROPERTY_INSURANCE"}, "max_doc_age_in_months": null}]}, {"key": "property_insurance_gustavo", "title_de": "Gebäudeversicherungsausweis", "title_en": "Insurance Certificate on Building", "title_fr": "Attestation assurance des batiments", "title_it": "Certificato assicurazione dello stabile", "desc_de": "", "desc_en": "", "desc_fr": "", "desc_it": "", "entity": 3, "field": "gustavo", "values": "True", "strictness": "required", "order": 27, "business_case_types": ["NEW_PURCHASE", "NEW_REFINANCE", "DEBTOR_CHANGE"], "document_options": [{"document_category": {"name": "PROPERTY_INSURANCE"}, "max_doc_age_in_months": null}]}, {"key": "condominium_certificate", "title_de": "Stockwerkeigentum: Nutzungs- und Verwaltungsreglement", "title_en": "Condominium owner regulations", "title_fr": "Règlement de copropriété", "title_it": "Regolamenti dei proprietari di condominio", "desc_de": "", "desc_en": "", "desc_fr": "", "desc_it": "", "entity": 3, "field": "get_type_key", "values": "STWE", "strictness": "optional", "order": 28, "business_case_types": ["NEW_PURCHASE"], "document_options": [{"document_category": {"name": "USER_REGULATIONS_CONDOMINIUM"}, "max_doc_age_in_months": null}, {"document_category": {"name": "FOUNDATION_CERTIFICATE_CONDOMINIUM"}, "max_doc_age_in_months": null}]}, {"key": "tenant_directory", "title_de": "<PERSON><PERSON><PERSON>iegel", "title_en": "Tenant Directory", "title_fr": "Etat locatif", "title_it": "Contratto di locazione", "desc_de": "", "desc_en": "", "desc_fr": "", "desc_it": "", "entity": 3, "field": "for_rent", "values": "True", "strictness": "required", "order": 29, "business_case_types": ["NEW_PURCHASE", "NEW_REFINANCE", "DEBTOR_CHANGE", "INCREASE"], "document_options": [{"document_category": {"name": "TENANT_DIRECTORY"}, "max_doc_age_in_months": null}]}, {"key": "renovation_fund_optional", "title_de": "Erneuerungsfonds", "title_en": "Renovation Fund", "title_fr": "Fonds de rénovation", "title_it": "Fondo di rinnovo", "desc_de": "", "desc_en": "", "desc_fr": "", "desc_it": "", "entity": 3, "field": "get_type_key", "values": "STWE", "strictness": "optional", "order": 30, "business_case_types": ["NEW_PURCHASE"], "document_options": [{"document_category": {"name": "RENOVATION_FUND"}, "max_doc_age_in_months": null}]}, {"key": "renovation_fund", "title_de": "Erneuerungsfonds", "title_en": "Renovation Fund", "title_fr": "Fonds de rénovation", "title_it": "Fondo di rinnovo", "desc_de": "", "desc_en": "", "desc_fr": "", "desc_it": "", "entity": 3, "field": "get_type_key", "values": "STWE", "strictness": "required", "order": 31, "business_case_types": ["NEW_REFINANCE"], "document_options": [{"document_category": {"name": "RENOVATION_FUND"}, "max_doc_age_in_months": null}]}, {"key": "building_rights", "title_de": "Baurechtsvertrag", "title_en": "Building Rights Agreement", "title_fr": "Contrat de superficie", "title_it": "Contratto di diritto di superficie", "desc_de": "", "desc_en": "", "desc_fr": "", "desc_it": "", "entity": 3, "field": "has_use_restrictions", "values": "True", "strictness": "required", "order": 32, "business_case_types": ["NEW_PURCHASE", "NEW_REFINANCE", "DEBTOR_CHANGE"], "document_options": [{"document_category": {"name": "BUILDING_RIGHTS_AGREEMENT"}, "max_doc_age_in_months": null}]}, {"key": "construction_contract", "title_de": "GU, TU", "title_en": "General contractor, total contractor", "title_fr": "Entrepreneur <PERSON><PERSON><PERSON><PERSON>, entrepreneur total", "title_it": "Impresa generale, impresa totale", "desc_de": "", "desc_en": "", "desc_fr": "", "desc_it": "", "entity": 3, "field": "has_construction_contract", "values": "True", "strictness": "required", "order": 33, "business_case_types": ["NEW_PURCHASE"], "document_options": [{"document_category": {"name": "CONSTRUCTION_CONTRACT"}, "max_doc_age_in_months": null}, {"document_category": {"name": "CONTRACT_GENERAL_CONTRACTOR"}, "max_doc_age_in_months": null}, {"document_category": {"name": "CONTRACT_TOTAL_CONTRACTOR"}, "max_doc_age_in_months": null}]}, {"key": "renovation_costs", "title_de": "Kosten für Umbau oder Renovation", "title_en": "Costs for conversion or renovation", "title_fr": "Frais de transformation ou de rénovation", "title_it": "Costi di conversione o ristrutturazione", "desc_de": "", "desc_en": "", "desc_fr": "", "desc_it": "", "entity": 3, "field": "has_reconstructions", "values": "True", "strictness": "required", "order": 34, "business_case_types": ["NEW_PURCHASE", "NEW_REFINANCE", "DEBTOR_CHANGE"], "document_options": [{"document_category": {"name": "CONSTRUCTION_QUOTATION"}, "max_doc_age_in_months": null}, {"document_category": {"name": "CONSTRUCTION_COST_SUMMARY"}, "max_doc_age_in_months": null}, {"document_category": {"name": "CONSTRUCTION_ACCOUNT"}, "max_doc_age_in_months": null}, {"document_category": {"name": "CONSTRUCTION_COST_ESTIMATE"}, "max_doc_age_in_months": null}]}, {"key": "renovation_costs_optional", "title_de": "Kosten für Umbau oder Renovation", "title_en": "Costs for conversion or renovation", "title_fr": "Frais de transformation ou de rénovation", "title_it": "Costi di conversione o ristrutturazione", "desc_de": "", "desc_en": "", "desc_fr": "", "desc_it": "", "entity": 3, "field": "has_reconstructions", "values": "True", "strictness": "optional", "order": 35, "business_case_types": ["INCREASE"], "document_options": [{"document_category": {"name": "CONSTRUCTION_COST_SUMMARY"}, "max_doc_age_in_months": null}, {"document_category": {"name": "CONSTRUCTION_COST_ESTIMATE"}, "max_doc_age_in_months": null}, {"document_category": {"name": "CONSTRUCTION_QUOTATION"}, "max_doc_age_in_months": null}, {"document_category": {"name": "CONSTRUCTION_ACCOUNT"}, "max_doc_age_in_months": null}]}]}