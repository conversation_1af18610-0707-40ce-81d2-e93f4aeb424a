import json
import structlog
from pathlib import Path

import pytest
from django.core.management import call_command

from core.temporary_path import temporary_path
from doccheck.export import export_doccheck, import_doccheck, create_export
from doccheck.models import DocCheck

logger = structlog.get_logger()


@pytest.fixture()
def load_doccheck(django_db_blocker):
    with django_db_blocker.unblock():
        call_command("loaddata", "initial.json", app="doccheck")


def test_export_doccheck(db, load_doccheck):
    # dest_path = Path(__file__).parent / f"data/doccheck_bekb_dev.json"
    # export_doccheck('bekb_dev', dest_path)
    with temporary_path() as path:
        dest_path = path / "dump.json"
        export_doccheck("bekb_dev", dest_path)
        expected_file_path = Path(__file__).parent / "data/doccheck_bekb_dev.json"

        exported_data_dict = json.loads(dest_path.read_text())
        expected_dict = json.loads(expected_file_path.read_text())

        for key, value in exported_data_dict.items():
            assert exported_data_dict[key] == expected_dict[key]


def test_import_doccheck(db, load_doccheck):
    export_before_import = create_export("bekb_dev")
    source_path = Path(__file__).parent / "data/doccheck_bekb_dev.json"
    assert DocCheck.objects.count() == 1

    import_doccheck(source_path, "new_test_key")
    assert DocCheck.objects.count() == 2

    export_after_import = create_export("bekb_dev")
    # existing doccheck should not be altered by importing a new one
    assert export_before_import == export_after_import
