from adminsortable.models import SortableMixin
from django.db import models
from django.db.models import CASCADE

from core.behaviors import Timestampable


class DocCheck(Timestampable):
    key = models.CharField(max_length=255, unique=True)
    description = models.TextField(blank=True, null=True)

    def __str__(self):
        return self.key


class BusinessCaseType(Timestampable, SortableMixin):
    doc_check = models.ForeignKey(DocCheck, on_delete=CASCADE)
    key = models.CharField(max_length=255)
    name_de = models.CharField(max_length=255, blank=True, null=True)
    description_de = models.TextField(blank=True, null=True)
    name_en = models.CharField(max_length=255, blank=True, null=True)
    description_en = models.TextField(blank=True, null=True)
    name_fr = models.CharField(max_length=255, blank=True, null=True)
    description_fr = models.TextField(blank=True, null=True)
    name_it = models.CharField(max_length=255, blank=True, null=True)
    description_it = models.TextField(blank=True, null=True)
    order = models.PositiveIntegerField(default=0, editable=False)

    class Meta:
        unique_together = ["doc_check", "key"]
        ordering = ["order"]

    def __str__(self):
        return self.key


class Case(Timestampable):
    doc_check = models.ForeignKey(DocCheck, on_delete=CASCADE)
    business_case_type = models.ForeignKey(
        BusinessCaseType, blank=False, null=False, on_delete=CASCADE
    )


class Entity(models.IntegerChoices):
    Case = 1
    Person = 2
    RealEstateProperty = 3


class FieldType(models.IntegerChoices):
    CHOICE = 1
    BOOLEAN = 2


class Field(Timestampable, SortableMixin):
    doc_check = models.ForeignKey(DocCheck, on_delete=CASCADE)
    shown_for_the_following_business_case_types = models.ManyToManyField(
        BusinessCaseType
    )
    entity = models.IntegerField(choices=Entity.choices)
    key = models.CharField(max_length=50)
    name_de = models.CharField(max_length=255, blank=True, null=True)
    name_en = models.CharField(max_length=255, blank=True, null=True)
    name_fr = models.CharField(max_length=255, blank=True, null=True)
    name_it = models.CharField(max_length=255, blank=True, null=True)
    order = models.PositiveIntegerField(default=0, editable=False)
    type = models.IntegerField(choices=FieldType.choices)
    choices_default = models.ForeignKey(
        "FieldChoice", blank=True, null=True, on_delete=CASCADE, related_name="default"
    )

    def __str__(self):
        return f"{self.entity}/{self.key}"

    class Meta:
        unique_together = ["doc_check", "entity", "key"]
        ordering = ["order"]


class FieldChoice(Timestampable, SortableMixin):
    field = models.ForeignKey(Field, on_delete=CASCADE)
    key = models.CharField(max_length=50)
    name_de = models.CharField(max_length=255, blank=True, null=True)
    name_en = models.CharField(max_length=255, blank=True, null=True)
    name_fr = models.CharField(max_length=255, blank=True, null=True)
    name_it = models.CharField(max_length=255, blank=True, null=True)
    order = models.PositiveIntegerField(default=0, editable=False)

    class Meta:
        unique_together = ["field", "key"]
        ordering = ["order"]

    def __str__(self):
        return f"{self.field.key}/{self.key}"


class Person(Timestampable):
    case = models.ForeignKey(Case, on_delete=CASCADE)
    main_income = models.ForeignKey(
        FieldChoice,
        on_delete=CASCADE,
        related_name="income_field_choice",
        limit_choices_to={"field__key": "main_income"},
    )
    pledge_pillar_3_account = models.BooleanField(default=False)
    pledge_pillar_3_insurance = models.BooleanField(default=False)
    has_divorce = models.BooleanField(default=False)
    has_liabilities = models.BooleanField(default=False)

    @property
    def get_main_income_key(self):
        return self.main_income.key

    def __str__(self):
        return str(self.uuid)


class RealEstateProperty(Timestampable):
    case = models.ForeignKey(Case, on_delete=CASCADE)
    type = models.ForeignKey(
        FieldChoice,
        on_delete=CASCADE,
        related_name="type_field_choice",
        limit_choices_to={"field__key": "type"},
    )
    address_canton = models.ForeignKey(
        FieldChoice,
        on_delete=CASCADE,
        related_name="canton_field_choice",
        limit_choices_to={"field__key": "address_canton"},
    )
    for_rent = models.BooleanField(default=False)
    purchase_year_lt_2y = models.BooleanField(default=False)
    has_use_restrictions = models.BooleanField(default=False)
    has_reconstructions = models.BooleanField(default=False)
    has_construction_contract = models.BooleanField(default=False)

    @property
    def get_type_key(self):
        return self.type.key

    @property
    def get_address_canton_key(self):
        return self.address_canton.key

    @property
    def gustavo(self):
        if self.address_canton:
            if self.address_canton.key in ("GE", "UR", "SZ", "TI", "AI", "VS", "OW"):
                return True

        return False

    def __str__(self):
        return str(self.uuid)


class CompletenessRule(Timestampable, SortableMixin):
    doc_check = models.ForeignKey(DocCheck, on_delete=CASCADE)
    key = models.CharField(max_length=255)
    business_case_types = models.ManyToManyField(BusinessCaseType)
    title_de = models.CharField(max_length=255, blank=True, null=True)
    title_en = models.CharField(max_length=255, blank=True, null=True)
    title_fr = models.CharField(max_length=255, blank=True, null=True)
    title_it = models.CharField(max_length=255, blank=True, null=True)
    desc_de = models.TextField(blank=True, null=True)
    desc_en = models.TextField(blank=True, null=True)
    desc_fr = models.TextField(blank=True, null=True)
    desc_it = models.TextField(blank=True, null=True)
    entity = models.IntegerField(choices=Entity.choices)
    # if the context specifically points to a field in the linked model
    field = models.CharField(max_length=50, blank=True, null=True)
    # if the linked field needs to have one of the specified values
    values = models.CharField(max_length=255, blank=True, null=True)
    STRICTNESS = [(i, i) for i in "required,optional".split(",")]
    strictness = models.CharField(
        max_length=20, choices=STRICTNESS, blank=False, null=False
    )
    order = models.PositiveIntegerField(default=0, editable=False)

    class Meta:
        unique_together = ["doc_check", "key"]
        ordering = ["order"]

    def __str__(self):
        return f"{self.doc_check}/{self.key}"


class DocumentCategory(Timestampable):
    doc_check = models.ForeignKey(DocCheck, on_delete=CASCADE)
    name = models.CharField(max_length=255)

    class Meta:
        unique_together = ["doc_check", "name"]

    def __str__(self):
        return f"{self.name}"


class DocumentOption(Timestampable):
    # If there are more than one DocumentRequirement linked to the same requirement, they are always meant as an OR group.
    # If there are multiple DocumentRequirement in an AND group, split them to separate requirements.
    completeness_rule = models.ForeignKey(CompletenessRule, on_delete=CASCADE)
    document_category = models.ForeignKey(
        DocumentCategory, on_delete=CASCADE, blank=True, null=True
    )
    max_doc_age_in_months = models.IntegerField(blank=True, null=True)

    def __str__(self):
        if self.document_category and self.max_doc_age_in_months:
            return f"{self.completeness_rule.key}/{self.document_category.name} - {self.max_doc_age_in_months}"
        elif self.document_category:
            return (
                f"{self.completeness_rule.key}/{self.document_category.name} - unknown"
            )
        else:
            return f"{self.completeness_rule.key}/unknown"
