from typing import List, Optional
from uuid import UUID

from pydantic import BaseModel

from doccheck.models import Entity


class FieldChoice(BaseModel):
    uuid: UUID
    key: str
    name_de: str
    name_en: str
    name_fr: str
    name_it: str


class Field(BaseModel):
    uuid: UUID
    entity: str
    key: str
    name_de: str
    name_en: str
    name_fr: str
    name_it: str
    choices: Optional[List[FieldChoice]] = []


class PersonOut(BaseModel):
    uuid: UUID
    name_de: str
    name_en: str
    name_fr: str
    name_it: str
    main_income: Optional[str] = None
    pledge_pillar_3_account: Optional[bool] = None
    pledge_pillar_3_insurance: Optional[bool] = None
    has_divorce: Optional[bool] = None
    has_liabilities: Optional[bool] = None


class PersonUpdate(BaseModel):
    main_income: Optional[str] = None
    pledge_pillar_3_account: Optional[bool] = None
    pledge_pillar_3_insurance: Optional[bool] = None
    has_divorce: Optional[bool] = None
    has_liabilities: Optional[bool] = None


class PersonCreate(BaseModel):
    main_income: Optional[str] = None
    pledge_pillar_3_account: Optional[bool] = None
    pledge_pillar_3_insurance: Optional[bool] = None
    has_divorce: Optional[bool] = None
    has_liabilities: Optional[bool] = None


class RealEstatePropertyOut(BaseModel):
    uuid: UUID
    name_de: str
    name_en: str
    name_fr: str
    name_it: str
    type: Optional[str] = None
    address_canton: Optional[str] = None
    for_rent: Optional[bool] = None
    purchase_year_lt_2y: Optional[bool] = None
    has_use_restrictions: Optional[bool] = None
    has_reconstructions: Optional[bool] = None
    has_construction_contract: Optional[bool] = None
    gustavo: Optional[bool] = None


class RealEstatePropertyUpdate(BaseModel):
    type: Optional[str] = None
    address_canton: Optional[str] = None
    for_rent: Optional[bool] = None
    purchase_year_lt_2y: Optional[bool] = None
    has_use_restrictions: Optional[bool] = None
    has_reconstructions: Optional[bool] = None
    has_construction_contract: Optional[bool] = None


class RealEstatePropertyCreate(BaseModel):
    type: Optional[str] = None
    address_canton: Optional[str] = None
    for_rent: Optional[bool] = None
    purchase_year_lt_2y: Optional[bool] = None
    has_use_restrictions: Optional[bool] = None
    has_reconstructions: Optional[bool] = None
    has_construction_contract: Optional[bool] = None


class CaseOut(BaseModel):
    uuid: UUID
    business_case_type: str


class CaseDetailOut(BaseModel):
    uuid: UUID
    business_case_type: str
    persons: Optional[List[PersonOut]] = []
    real_estate_property: Optional[RealEstatePropertyOut] = None


class CaseUpdate(BaseModel):
    business_case_type: str


class CompletenessRule(BaseModel):
    key: str
    title_de: Optional[str] = None
    title_en: Optional[str] = None
    title_fr: Optional[str] = None
    title_it: Optional[str] = None
    desc_de: Optional[str] = None
    desc_en: Optional[str] = None
    desc_fr: Optional[str] = None
    desc_it: Optional[str] = None
    entity: Entity
    field: Optional[str] = None
    values: Optional[str] = None
    strictness: Optional[str] = "required"
    order: Optional[int] = None


class DocumentOption(BaseModel):
    uuid: UUID
    document_category: str
    max_doc_age_in_months: Optional[int] = None


class DocumentRequirement(BaseModel):
    case_uuid: UUID
    context_model_uuid: UUID
    rule_key: str
    rule_title_de: Optional[str] = None
    rule_title_en: Optional[str] = None
    rule_title_fr: Optional[str] = None
    rule_title_it: Optional[str] = None
    rule_desc_de: Optional[str] = None
    rule_desc_en: Optional[str] = None
    rule_desc_fr: Optional[str] = None
    rule_desc_it: Optional[str] = None
    rule_strictness: Optional[str] = None
    rule_order: int
    document_options: List[DocumentOption]
