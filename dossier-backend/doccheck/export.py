import json
import structlog
from pathlib import Path
from typing import List, Optional

from django.db import transaction
from pydantic import BaseModel, TypeAdapter

import doccheck
import dossier
from doccheck.models import (
    DocCheck,
    BusinessCaseType,
    CompletenessRule,
    Field,
    FieldChoice,
    DocumentOption,
    DocumentCategory,
)

logger = structlog.get_logger()


class DossierBusinessCaseTypeImport(BaseModel):
    account_key: str
    key: str
    name_de: Optional[str] = None
    description_de: Optional[str] = None
    name_en: Optional[str] = None
    description_en: Optional[str] = None
    name_fr: Optional[str] = None
    description_fr: Optional[str] = None
    name_it: Optional[str] = None
    description_it: Optional[str] = None
    order: Optional[int] = None


class BusinessCaseTypeExport(BaseModel):
    key: str
    name_de: Optional[str] = None
    description_de: Optional[str] = None
    name_en: Optional[str] = None
    description_en: Optional[str] = None
    name_fr: Optional[str] = None
    description_fr: Optional[str] = None
    name_it: Optional[str] = None
    description_it: Optional[str] = None
    order: int


class DocumentCategoryExport(BaseModel):
    name: str


class DocumentOptionExport(BaseModel):
    document_category: Optional[DocumentCategoryExport] = None
    max_doc_age_in_months: Optional[int] = None


class CompletenessRuleExport(BaseModel):
    key: str
    title_de: Optional[str] = None
    title_en: Optional[str] = None
    title_fr: Optional[str] = None
    title_it: Optional[str] = None
    desc_de: Optional[str] = None
    desc_en: Optional[str] = None
    desc_fr: Optional[str] = None
    desc_it: Optional[str] = None
    entity: int
    field: Optional[str] = None
    values: Optional[str] = None
    strictness: Optional[str] = None
    order: int
    business_case_types: List[str] = []
    document_options: List[DocumentOptionExport] = []


class FieldChoiceExport(BaseModel):
    key: str
    name_de: str
    name_en: str
    name_fr: str
    name_it: str
    order: int


class FieldExport(BaseModel):
    entity: int
    key: str
    name_de: str
    name_en: str
    name_fr: str
    name_it: str
    type: int
    choices: List[FieldChoiceExport] = []
    choices_default: Optional[str] = None
    shown_for_the_following_business_case_types: List[str] = []


class DocCheckExport(BaseModel):
    key: str
    description: Optional[str] = None
    business_case_types: List[BusinessCaseTypeExport] = []
    fields: List[FieldExport] = []
    completeness_rules: List[CompletenessRuleExport] = []


def create_export(doc_check_key):
    doc_check = DocCheck.objects.get(key=doc_check_key)
    doc_check_export = DocCheckExport(
        key=doc_check.key,
        description=doc_check.description,
        business_case_types=[
            BusinessCaseTypeExport(
                key=bct.key,
                name_de=bct.name_de,
                description_de=bct.description_de,
                name_en=bct.name_en,
                description_en=bct.description_en,
                name_fr=bct.name_fr,
                description_fr=bct.description_fr,
                name_it=bct.name_it,
                description_it=bct.description_it,
                order=bct.order,
            )
            for bct in doc_check.businesscasetype_set.all()
        ],
        fields=[
            FieldExport(
                entity=field.entity,
                key=field.key,
                name_de=field.name_de,
                name_en=field.name_en,
                name_fr=field.name_fr,
                name_it=field.name_it,
                type=field.type,
                choices=[
                    FieldChoiceExport(
                        key=choice.key,
                        name_de=choice.name_de,
                        name_en=choice.name_en,
                        name_fr=choice.name_fr,
                        name_it=choice.name_it,
                        order=choice.order,
                    )
                    for choice in field.fieldchoice_set.all()
                ],
                choices_default=(
                    field.choices_default.key if field.choices_default else None
                ),
                shown_for_the_following_business_case_types=[
                    bct.key
                    for bct in field.shown_for_the_following_business_case_types.all()
                ],
            )
            for field in doc_check.field_set.all()
        ],
        completeness_rules=[
            CompletenessRuleExport(
                key=rule.key,
                title_de=rule.title_de,
                title_en=rule.title_en,
                title_fr=rule.title_fr,
                title_it=rule.title_it,
                desc_de=rule.desc_de,
                desc_en=rule.desc_en,
                desc_fr=rule.desc_fr,
                desc_it=rule.desc_it,
                entity=rule.entity,
                field=rule.field,
                values=rule.values,
                strictness=rule.strictness,
                order=rule.order,
                business_case_types=[bct.key for bct in rule.business_case_types.all()],
                document_options=[
                    DocumentOptionExport(
                        document_category=(
                            DocumentCategoryExport(name=option.document_category.name)
                            if option.document_category
                            else None
                        ),
                        max_doc_age_in_months=option.max_doc_age_in_months,
                    )
                    for option in rule.documentoption_set.all()
                ],
            )
            for rule in doc_check.completenessrule_set.all()
        ],
    )
    return doc_check_export


def export_doccheck(doc_check_key: str, dest_path: Path):
    doc_check_detail_export = create_export(doc_check_key)
    dest_path.write_text(doc_check_detail_export.model_dump_json())


def import_doccheck(source_path: Path, doc_check_key: str = None):
    with transaction.atomic():
        if doc_check_key:
            logger.info(
                f"importing doccheck from {source_path} using doc check key {doc_check_key}"
            )
        else:
            logger.info(
                f"importing doccheck from {source_path} using doc check key provided in this file"
            )

        export = DocCheckExport.model_validate_json(source_path.read_text())
        if not doc_check_key:
            doc_check_key = export.key
        doc_check, created = DocCheck.objects.update_or_create(
            key=doc_check_key,
            defaults={"description": export.description},
        )
        if created:
            logger.info(f"Created new doccheck {doc_check_key}")

        business_case_types = {}
        for bct in export.business_case_types:
            bct_defaults = bct.model_dump()
            del bct_defaults["key"]
            business_case_types[bct.key], created = (
                BusinessCaseType.objects.update_or_create(
                    doc_check=doc_check,
                    key=bct.key,
                    defaults=bct_defaults,
                )
            )
            if created:
                logger.info(f"Created new business_case_type {bct.key}")

        fields = {}
        field_choices = {}
        for field in export.fields:
            field_defaults = field.model_dump()
            del field_defaults["entity"]
            del field_defaults["key"]
            del field_defaults["choices"]
            del field_defaults["choices_default"]
            del field_defaults["shown_for_the_following_business_case_types"]
            fields[field.key], _ = Field.objects.update_or_create(
                doc_check=doc_check,
                entity=field.entity,
                key=field.key,
                defaults=field_defaults,
            )

            for choice in field.choices:
                choice_defaults = choice.model_dump()
                del choice_defaults["key"]
                (
                    field_choices[f"{field.key}/{choice.key}"],
                    _,
                ) = FieldChoice.objects.update_or_create(
                    field=fields[field.key],
                    key=choice.key,
                    defaults=choice_defaults,
                )

            if field.choices_default:
                fields[field.key].choices_default = field_choices[
                    f"{field.key}/{field.choices_default}"
                ]
            bcts = [
                business_case_types[bct]
                for bct in field.shown_for_the_following_business_case_types
            ]
            fields[field.key].shown_for_the_following_business_case_types.set(bcts)
            fields[field.key].save()

        rules = {}
        for rule in export.completeness_rules:
            rule_defaults = rule.model_dump()
            del rule_defaults["key"]
            del rule_defaults["business_case_types"]
            del rule_defaults["document_options"]
            rules[rule.key], _ = CompletenessRule.objects.update_or_create(
                doc_check=doc_check, key=rule.key, defaults=rule_defaults
            )

            for option in rule.document_options:
                doccat = None
                if option.document_category:
                    doccat, _ = DocumentCategory.objects.get_or_create(
                        doc_check=doc_check, name=option.document_category.name
                    )
                DocumentOption.objects.get_or_create(
                    completeness_rule=rules[rule.key],
                    document_category=doccat,
                    max_doc_age_in_months=option.max_doc_age_in_months,
                )

            bcts = [business_case_types[bct] for bct in rule.business_case_types]
            rules[rule.key].business_case_types.set(bcts)
            rules[rule.key].save()
        return doc_check


def export_doccat_names(doc_check_key: str, dest_path: Path):
    doc_check = DocCheck.objects.get(key=doc_check_key)
    assert doc_check is not None
    doccat_names = sorted([dc.name for dc in doc_check.documentcategory_set.all()])

    export_content = json.dumps(
        doccat_names, indent=2
    )  #'[\n"' + '",\n"'.join(doccat_names) + '"\n]'
    print(export_content)
    dest_path.write_text(export_content)

    text = dest_path.read_text()
    doccat_names = TypeAdapter(List[str]).validate_json(text)


def import_doccat_names(source_path: Path, doc_check_key: str):
    doc_check, _ = DocCheck.objects.update_or_create(key=doc_check_key)
    text = Path(source_path).read_text()
    doccat_names = TypeAdapter(List[str]).validate_json(text)
    for name in doccat_names:
        dc, created = DocumentCategory.objects.update_or_create(
            doc_check=doc_check, name=name
        )
        if created:
            logger.info(f"Created doccat category: {dc}")


def sync_doccat_names_from_account(account_key: str, doc_check_key: str):
    logger.info(
        f"sync_doccat_names_from_account({account_key = }, {doc_check_key = })..."
    )
    account_doc_cats = (
        dossier.models.DocumentCategory.objects.filter(account__key=account_key)
        .order_by("id")
        .all()
    )
    assert len(account_doc_cats) > 0

    doc_check = DocCheck.objects.filter(key=doc_check_key).first()
    assert doc_check is not None
    for account_doc_cat in account_doc_cats:
        dc, created = doccheck.models.DocumentCategory.objects.update_or_create(
            name=account_doc_cat.name, doc_check=doc_check
        )
        if created:
            logger.info(f"Created doc cat in doccheck: {dc.name}")


def sync_businesscase_types_from_account(account_key: str, doc_check_key: str):
    logger.info(
        f"sync_businesscase_types_from_account({account_key = }, {doc_check_key = })..."
    )
    account_bc_types = (
        dossier.models.BusinessCaseType.objects.filter(account__key=account_key)
        .order_by("key")
        .all()
    )
    assert len(account_bc_types) > 4

    doc_check = DocCheck.objects.filter(key=doc_check_key).first()
    assert doc_check is not None
    for bct in account_bc_types:
        _, created = BusinessCaseType.objects.update_or_create(
            dict(
                name_de=bct.name_de,
                description_de=bct.description_de,
                name_en=bct.name_en,
                description_en=bct.description_en,
                name_fr=bct.name_fr,
                description_fr=bct.description_fr,
                name_it=bct.name_it,
                description_it=bct.description_it,
                order=bct.order,
            ),
            doc_check=doc_check,
            key=bct.key,
        )
        logger.info(
            f'{"created" if created else "updated"} business case type {bct.key}'
        )


def import_business_case_types(source_path: Path, doc_check_key: str):
    doc_check, _ = DocCheck.objects.update_or_create(key=doc_check_key)
    dossier_bcts = TypeAdapter(List[DossierBusinessCaseTypeImport]).validate_json(
        source_path.read_text()
    )
    for bct in dossier_bcts:
        _, created = BusinessCaseType.objects.update_or_create(
            dict(
                name_de=bct.name_de,
                description_de=bct.description_de,
                name_en=bct.name_en,
                description_en=bct.description_en,
                name_fr=bct.name_fr,
                description_fr=bct.description_fr,
                name_it=bct.name_it,
                description_it=bct.description_it,
                order=bct.order,
            ),
            doc_check=doc_check,
            key=bct.key,
        )
        logger.info(
            f'{"created" if created else "updated"} business case type {bct.key}'
        )
