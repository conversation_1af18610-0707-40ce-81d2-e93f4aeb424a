from channels.db import database_sync_to_async
from django.conf import settings

from ninja.security import <PERSON>tt<PERSON><PERSON><PERSON><PERSON>
from typing import Union

import structlog

from projectconfig.authentication import authenticate_from_account
from swissfex.schemas import schemas

logger = structlog.get_logger()


# TODO: Async Not tested or used yet
async def authenticate_async(
    request, token, *args, **kw
) -> Union[schemas.DossierCreateJWT, None]:
    return await database_sync_to_async(authenticate_from_account(token))


class SwissfexJWTAuth(HttpBearer):
    sync_view_name = "api:ninja.operation._sync_view"

    def authenticate(
        self, request, token, *args, **kw
    ) -> Union[schemas.DossierCreateJWT, None]:
        jwt = authenticate_from_account(token)

        # dossier manager should not be required,
        # because external api allows access to all dossier which are owned by the user (which is the sevice account)
        request.is_manager = True

        # no external_dossier_id filtering is required for the external_api, because there is no impersonation
        # everything is done as the service account

        # needs "api_role" in user_roles, else no access
        if settings.API_ROLE not in jwt.user_roles:
            return
        return jwt
