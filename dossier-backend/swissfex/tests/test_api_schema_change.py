import json

import django

from core.helpers import compare_datastructures
from swissfex.schemas.data import DATA_PATH


def test_openapi_schema(client: django.test.client.Client):
    # Test to ensure that the underlying json Api schema has not changed
    res = client.get("/partner/swissfex/api/0.7/openapi.json")
    assert res.status_code == 200

    with open(DATA_PATH / "swissfex_openapi_3_1_0_2025_01_23.json") as file:
        data_2 = json.load(file)

    compare_datastructures(res.json(), data_2)
