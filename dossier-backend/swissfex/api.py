from datetime import <PERSON><PERSON><PERSON>
from enum import Enum
from typing import List, Optional
from uuid import UUID

import pika
import structlog
from django.db.models import QuerySet
from django.http import Http404
from django.shortcuts import get_object_or_404
from django.utils import timezone
from ninja import File, NinjaAPI, UploadedFile, Form
from ninja.errors import HttpError, ValidationError
from pydantic import constr

from core.publisher import publish
from core.schema import Error, Message
from dossier import models as dossier_models
from dossier import schemas as dossier_schema
from dossier.helpers_access_check import get_dossier_with_access_check
from dossier.helpers_api import handle_api_validation_error
from dossier.helpers_model_copier import copy_dossier_models, copy_single_dossier
from dossier.helpers_v2 import get_qs_for_semantic_documents
from dossier.models import (
    Account,
    <PERSON>ssier,
    OriginalFile,
    <PERSON>ssierExport,
    <PERSON><PERSON>r<PERSON><PERSON>,
    DossierCopyStatus,
)
from dossier.processing_config import OriginalFileProcessingConfig
from dossier.schemas import (
    DossierCopyContentsIntoExistingDossierRequest,
)
from dossier.services import get_semantic_document_last_change
from dossier import services_external as dossier_services_external
from dossier.services_external import create_dossier_api, update_dossier_api
from dossier_zipper.schemas import DossierZipRequestV1
from dossier_zipper.workers import generate_dossier_zip_request
from django.conf import settings
from projectconfig.settings import (
    ASYNC_DOSSIER_SEMANTIC_DOCUMENT_PDF_WORKER_ROUTING_KEY,
    ASYNC_DOSSIER_ZIPPER_WORKER_V2_ROUTING_KEY,
    MAX_LENGTH_SEMANTIC_DOCUMENT_TITLE_SUFFIX,
)
from semantic_document import (
    helpers as semantic_document_helpers,
    models as semantic_document_models,
    schemas as semantic_document_schemas,
)

from semantic_document.models import SemanticDocument, SemanticPage
from semantic_document.services import (
    export_semantic_document_wrapper_with_access_check,
    map_confidence,
)
from swissfex.schemas import schemas
from swissfex.auth import SwissfexJWTAuth
from swissfex.schemas.schemas import SemanticDocumentPDFExportRequest
from workers.models import SemanticDocumentExport
from workers.schemas import SemanticDocumentPDFRequestV1


logger = structlog.get_logger()


api = NinjaAPI(
    title="Hypodossier - Swissfex API",
    csrf=False,
    auth=SwissfexJWTAuth(),
    urls_namespace="swissfex-api",
    version="0.7.0",
)


@api.exception_handler(ValidationError)
def custom_validation_errors(request, exc):
    return handle_api_validation_error(api, logger, exc, request)


# @api.get("/dossier", response={200: List[schemas.Dossier]})
# def get_dossiers(request):
#     return [
#         map_dossier(dossier)
#         for dossier in dossier_models.Dossier.objects.filter(account__key=ACCOUNT_KEY, expiry_date__gt=now()).all()
#     ]


def map_dossier(dossier):
    return schemas.Dossier(
        uuid=dossier.uuid,
        external_dossier_id=str(dossier.external_id),
        updated_at=dossier.updated_at,
        created_at=dossier.created_at,
    )


# Token
@api.post(
    "/dossier/create",
    response={201: schemas.Dossier, 409: Error},
    url_name="create-dossier",
)
def create_dossier(request, dossier_create: schemas.CreateDossier):
    return create_dossier_api(
        request=request,
        dossier_create=dossier_create,
        language=dossier_create.language,
    )


@api.patch(
    "/dossier/",
    response={201: schemas.Dossier, 409: Error},
    url_name="update-dossier",
)
def update_dossier(request, dossier_change: schemas.ChangeDossier):
    """Updates a new Dossier based on the provided parameters"""

    return update_dossier_api(
        request=request,
        external_dossier_id=dossier_change.external_dossier_id,
        dossier_change=dossier_change,
    )


class OriginalFileForceAccessMode(str, Enum):
    READ_WRITE = "read_write"
    READ_ONLY = "read_only"


@api.post(
    "/dossier/{external_dossier_id}/copy",
    response={201: schemas.DossierCopyResponse, 409: Error},
    url_name="copy-dossier",
)
def copy_dossier(request, external_dossier_id: str, dossier_copy: schemas.CopyDossier):
    """Creates a new Dossier based on the provided parameters"""

    user: DossierUser = request.auth.get_user_or_create()

    # If manager is false
    # You will only see docs you created yourself
    dossier = get_dossier_with_access_check(
        dossier_user=user,
        is_manager=True,  # Fix this to do proper ownership
        external_dossier_id=external_dossier_id,
    )

    # Check if dossier already exists, and return a 409 conflict if it does

    if dossier_models.Dossier.objects.filter(
        account=user.account, external_id=dossier_copy.new_external_dossier_id
    ).exists():
        return 409, {
            "code": 409,
            "message": f"Dossier with external dossier id {dossier_copy.new_external_dossier_id} already exists",
        }

    new_instances = copy_dossier_models(
        dossier=dossier, external_id=dossier_copy.new_external_dossier_id
    )

    new_dossier: Dossier = next(iter(new_instances.values()))

    # Set optional additional parameters
    if dossier_copy.name:
        new_dossier.name = dossier_copy.name
    if dossier_copy.language:
        new_dossier.lang = dossier_copy.language

    new_dossier.save()

    if dossier_copy.access_mode:
        SemanticDocument._base_manager.filter(dossier=new_dossier).update(
            access_mode=dossier_copy.access_mode
        )

    if dossier_copy.include_deleted is False:
        SemanticPage._base_manager.filter(
            dossier=new_dossier, deleted_at__lte=timezone.now()
        ).delete()
        SemanticDocument._base_manager.filter(
            dossier=new_dossier, deleted_at__lte=timezone.now()
        ).delete()

    return 201, {
        "external_dossier_id": new_dossier.external_id,
        "dossier_uuid": new_dossier.uuid,
        "updated_at": new_dossier.updated_at,
        "created_at": new_dossier.created_at,
    }


@api.post(
    "/dossier/{external_dossier_id}/copy_async",
    response={201: schemas.DossierCopyAsyncResponse, 409: Error},
    url_name="copy-dossier-async",
)
def copy_dossier_async(
    request, external_dossier_id: str, dossier_copy: schemas.CopyDossier
):
    """Create a new dossier and dispatch a task to copy the contents of
    the source dossier into the new dossier

    The reason we do it this way is that Swissfex doesn't have a way of Polling for status
    but they need a valid (i.e. already created) dossier for their system.
    They are happy to have an empty Dossier, that progressively gets filled with content

    """

    user: DossierUser = request.auth.get_user_or_create()

    source_dossier = get_dossier_with_access_check(
        dossier_user=user,
        is_manager=True,  # Fix this to do proper ownership
        external_dossier_id=external_dossier_id,
    )

    # Check if dossier already exists, and return a 409 conflict if it does

    if dossier_models.Dossier.objects.filter(
        account=user.account, external_id=dossier_copy.new_external_dossier_id
    ).exists():
        return 409, {
            "code": 409,
            "message": f"Dossier with external dossier id {dossier_copy.new_external_dossier_id} already exists",
        }

    if dossier_models.Dossier._base_manager.filter(
        account=user.account, external_id=dossier_copy.new_external_dossier_id
    ).exists():
        return 409, {
            "code": 409,
            "message": f"Dossier with external dossier id {dossier_copy.new_external_dossier_id} has already been deleted",
        }

    # Check if dossier is already being copied
    if DossierCopyStatus.objects.filter(
        source_dossier_uuid=source_dossier.uuid,
        account=user.account,
        target_external_id=dossier_copy.new_external_dossier_id,
        done__isnull=False,
    ).exists():
        return 409, {
            "code": 409,
            "message": f"Dossier with external dossier id {dossier_copy.new_external_dossier_id} is being processed",
        }

    if DossierCopyStatus.objects.filter(
        source_dossier_uuid=source_dossier.uuid,
        account=user.account,
        target_external_id=dossier_copy.new_external_dossier_id,
    ).exists():
        return 409, {
            "code": 409,
            "message": f"Dossier with external dossier id {dossier_copy.new_external_dossier_id} is already processed",
        }

    # Create new dossier
    target_dossier = copy_single_dossier(
        dossier=source_dossier, external_id=dossier_copy.new_external_dossier_id
    )

    # Set optional additional parameters
    if dossier_copy.name:
        target_dossier.name = dossier_copy.name
    if dossier_copy.language:
        target_dossier.lang = dossier_copy.language

    target_dossier.save()

    # Dispatch request to copy contents
    publish(
        message=DossierCopyContentsIntoExistingDossierRequest(
            source_dossier_uuid=source_dossier.uuid,
            target_dossier_uuid=target_dossier.uuid,
            include_deleted=dossier_copy.include_deleted,
            access_mode=dossier_copy.access_mode,
        )
        .model_dump_json()
        .encode(),
        routing_key=settings.ASYNC_DOSSIER_EVENTS_WORKER_V1_QUEUE_NAME,
        properties=pika.BasicProperties(
            type=settings.ASYNC_DOSSIER_COPY_INTO_EXISTING_DOSSIER_V1_QUEUE_NAME,
        ),
    )

    # Create copy status entry, so we can keep track of progress
    DossierCopyStatus.objects.get_or_create(
        defaults={"source_dossier_uuid": source_dossier.uuid},
        account=user.account,
        target_external_id=dossier_copy.new_external_dossier_id,
    )

    return 201, {
        "external_dossier_id": dossier_copy.new_external_dossier_id,
        "uuid": target_dossier.uuid,
    }


@api.get(
    "/export/dossier/{external_dossier_id}/copy_status",
    response={200: schemas.DossierCopyStatus, 409: Error},
    url_name="copy-dossier-status",
)
def get_dossier_copy_status(request, external_dossier_id):
    # Check the status of an async dossier copy

    account = get_object_or_404(Account, key=request.auth.account_key)

    dossier_copy_status: DossierCopyStatus = get_object_or_404(
        DossierCopyStatus, target_external_id=external_dossier_id, account=account
    )

    status = schemas.ExportProcessingStatus.PROCESSING
    if dossier_copy_status.done:
        status = schemas.ExportProcessingStatus.PROCESSED

    return schemas.DossierCopyStatus(
        external_dossier_id=external_dossier_id,
        status=status,
        updated_at=dossier_copy_status.done,
    )


@api.post(
    "/dossier/{external_dossier_id}/original-file",
    response={201: dossier_schema.CreatedObjectReference, 400: Message, 409: Message},
    url_name="add-original-file",
)
def add_original_file(
    request,
    external_dossier_id: str,
    file: UploadedFile = File(...),
    allow_duplicate_and_rename: bool = Form(False),
    force_document_category_key: Optional[str] = Form(None),
    force_title_suffix: Optional[
        constr(max_length=MAX_LENGTH_SEMANTIC_DOCUMENT_TITLE_SUFFIX)
    ] = Form(None),
    force_external_semantic_document_id: Optional[str] = Form(None),
    force_access_mode: Optional[OriginalFileForceAccessMode] = Form(None),
    force_semantic_document_custom_attribute: Optional[str] = Form(None),
):
    valid_force_access_mode = ["read_write", "read_only"]
    if force_access_mode:
        if force_access_mode not in valid_force_access_mode:
            return (
                400,
                f"Invalid parameter value for 'force_access_mode'. Only values allowed are {valid_force_access_mode}",
            )
    if force_semantic_document_custom_attribute:
        if len(force_semantic_document_custom_attribute) > 255:
            return (
                400,
                "Maximum length of 'force_semantic_document_custom_attribute' is 255 characters.",
            )

    user: DossierUser = request.auth.get_user_or_create()

    # If manager is false
    # You will only see docs you created yourself
    dossier = get_dossier_with_access_check(
        dossier_user=user,
        is_manager=True,  # Fix this to do proper ownership
        external_dossier_id=external_dossier_id,
    )
    force_title_elements = [force_title_suffix] if force_title_suffix else []
    processing_config = OriginalFileProcessingConfig(
        force_document_category_key=force_document_category_key,
        force_title_elements=force_title_elements,
        enable_virus_scan=dossier.account.enable_virus_scan,
        max_num_pages_allowed_input=dossier.account.max_num_pages_allowed_input,
    )

    force_access_mode_param = (
        OriginalFile.OriginalFileForceAccessMode.READ_ONLY
        if force_access_mode == "read_only"
        else None
    )

    response_code, original_file = dossier_services_external.add_original_file(
        dossier=dossier,
        file=file,
        allow_duplicate_and_rename=allow_duplicate_and_rename,
        force_external_semantic_document_id=force_external_semantic_document_id,
        force_access_mode=force_access_mode_param,
        force_semantic_document_custom_attribute=force_semantic_document_custom_attribute,
        processing_config=processing_config,
    )

    return 201, original_file


@api.get(
    "/dossier/{external_dossier_id}/details",
    response={200: schemas.Dossier},
    url_name="dossier-details",
)
def get_dossier(request, external_dossier_id: str):
    dossier = get_dossier_with_access_check(
        dossier_user=request.auth.get_user_or_create(),
        is_manager=True,  # Fix this to do proper ownership
        external_dossier_id=external_dossier_id,
    )

    if dossier.expiry_date is not None and dossier.expiry_date < timezone.now():
        return {
            404: f"Dossier {dossier.uuid} with external id {dossier.external_id} has been deleted"
        }

    return map_dossier(dossier)


@api.get(
    "/dossier/{external_dossier_id}/file-status",
    response={200: schemas.DossierProcessingStatus},
    url_name="file-status",
)
def get_file_status(request, external_dossier_id: str):
    dossier = get_dossier_with_access_check(
        dossier_user=request.auth.get_user_or_create(),
        is_manager=True,  # Fix this to do proper ownership
        external_dossier_id=external_dossier_id,
    )

    original_files_qs = (
        OriginalFile.objects.filter(dossier=dossier)
        .prefetch_related("file", "extractedfile_set")
        .all()
    )

    original_files = []

    original_file: dossier_models.OriginalFile

    processed = 0
    processing = 0
    for original_file in original_files_qs:
        extracted_files = []
        extracted_file: dossier_models.ExtractedFile
        for extracted_file in original_file.extractedfile_set.all():
            extracted_files.append(
                schemas.ExtractedFile(
                    uuid=extracted_file.uuid,
                    path_from_original=extracted_file.path_from_original,
                    file_name=extracted_file.file.name,
                    created_at=extracted_file.created_at,
                    updated_at=extracted_file.updated_at,
                    file_url=extracted_file.file.get_fast_url(),  # use fast_url? or url?
                    status=schemas.FileStatus(extracted_file.status.lower()),
                )
            )

        original_files.append(
            schemas.OriginalFile(
                uuid=original_file.uuid,
                name=original_file.file.name,
                status=schemas.FileStatus(original_file.status.lower()),
                file_url=original_file.file.get_fast_url(),  # use fast_url? or url?
                created_at=original_file.created_at,
                updated_at=original_file.updated_at,
                extracted_files=extracted_files,
            )
        )

        if original_file.status.lower() == dossier_models.FileStatus.PROCESSED.lower():
            processed += 1
        elif (
            original_file.status.lower() == dossier_models.FileStatus.PROCESSING.lower()
        ):
            processing += 1

    progress = 0

    if processed + processing > 0:
        progress = int(processed / (processed + processing) * 100)

    return schemas.DossierProcessingStatus(
        dossier_uuid=dossier.uuid,
        external_id=dossier.external_id,
        progress=progress,  # Calculate as a percentage of extracted_files having status PROCESSED?
        original_files=original_files,
    )


@api.get(
    "/dossier/{external_dossier_id}/semantic-documents",
    response={200: List[schemas.SemanticDocument]},
    url_name="semantic-documents",
)
def get_semantic_documents(
    request,
    external_dossier_id: str,
    show_pages: bool = False,
    show_soft_deleted: bool = False,
    show_all_documents_for_soft_deleted: bool = False,
):
    """Return schemantic documents for a dossier
    if show_pages is true, then also return the pages for each document
    """
    dossier = get_dossier_with_access_check(
        dossier_user=request.auth.get_user_or_create(),
        is_manager=True,  # Fix this to do proper ownership
        external_dossier_id=external_dossier_id,
    )

    semantic_documents_queryset: QuerySet["SemanticDocument"] = (
        get_qs_for_semantic_documents(
            dossier=dossier,
            show_soft_deleted=show_soft_deleted,
            hide_empty_semantic_documents=True,
            # If True this flag will trigger that all documents are returned if show_soft_deleted==True
            show_all_documents_for_soft_deleted=show_all_documents_for_soft_deleted,
        ).prefetch_related("semantic_pages")
    )

    return [
        schemas.SemanticDocument(
            uuid=document.uuid,
            title=document.title,
            title_lang=document.dossier.lang,
            title_suffix=document.title_suffix,
            external_semantic_document_id=document.external_semantic_document_id,
            document_category_id=document.document_category.id,
            document_category_key=document.document_category.name,
            document_category_title_de=document.document_category.de,
            document_category_title_en=document.document_category.en,
            document_category_title_fr=document.document_category.fr,
            document_category_title_it=document.document_category.it,
            document_category_confidence=map_confidence(document.confidence_value),
            created_at=document.created_at,
            updated_at=document.updated_at,
            deleted_at=document.deleted_at if show_soft_deleted else None,
            last_change=get_semantic_document_last_change(document),
            access_mode=document.access_mode,
            custom_attribute=document.custom_attribute,
            semantic_pages=(
                [
                    schemas.SemanticPage(
                        uuid=page.uuid,
                        number=page.number,
                        image_url=page.processed_page.image.fast_url,
                        updated_at=page.updated_at,
                        deleted_at=page.deleted_at if show_soft_deleted else None,
                    )
                    for page in document.semantic_pages.all()
                ]
                if show_pages
                else []
            ),  # Only return pages if show_pages is true
        )
        for document in semantic_documents_queryset.all()
    ]


@api.patch(
    "/dossier/{external_dossier_id}/semantic-document/{semantic_document_uuid}",
    response={
        200: schemas.SemanticDocument,
        401: str,  # No Public key found for account
        409: Error,
    },
    url_name="semantic-document-update",
)
def update_semantic_document(
    request,
    external_dossier_id: str,
    semantic_document_uuid: str,
    payload: schemas.UpdateSemanticDocument,
):
    """Update a schemantic document for a dossier
    if show_pages is true, then also return the pages for each document
    """
    dossier = get_dossier_with_access_check(
        dossier_user=request.auth.get_user_or_create(),
        is_manager=True,  # Fix this to do proper ownership
        external_dossier_id=external_dossier_id,
    )

    semantic_documents_queryset: QuerySet = (
        get_qs_for_semantic_documents(
            dossier=dossier,
            show_soft_deleted=False,
            hide_empty_semantic_documents=True,
            show_all_documents_for_soft_deleted=True,
        )
        .prefetch_related("semantic_pages")
        .filter(uuid=semantic_document_uuid)
    )

    semantic_document: SemanticDocument = semantic_documents_queryset.first()

    if semantic_document is None:
        # Match behaviour of semantic_document.get_object_or_404
        raise Http404("Semantic Document not found")

    # document_category_key is actually the document_category.name in the database
    if (
        payload.document_category_key is not None
        and payload.document_category_key != ""
    ):
        semantic_document.document_category = get_object_or_404(
            dossier_models.DocumentCategory,
            name=payload.document_category_key,
            account=dossier.account,
        )

    if payload.title_suffix is not None:
        semantic_document.title_suffix = payload.title_suffix

    if payload.access_mode is not None:
        semantic_document.access_mode = payload.access_mode

    if (
        payload.external_semantic_document_id is not None
        and payload.external_semantic_document_id != external_dossier_id
    ):
        if SemanticDocument.objects.filter(
            dossier=dossier,
            external_semantic_document_id=payload.external_semantic_document_id,
        ).exists():
            return 409, {
                "code": 409,
                "message": f"Semantic Document with external id {payload.external_semantic_document_id} already exists",
            }
        semantic_document.external_semantic_document_id = (
            payload.external_semantic_document_id
        )

    if payload.custom_attribute is not None:
        if payload.custom_attribute == "":
            semantic_document.custom_attribute = None
        else:
            semantic_document.custom_attribute = payload.custom_attribute

    semantic_document.save()
    semantic_document.refresh_from_db()

    return schemas.SemanticDocument(
        uuid=semantic_document.uuid,
        title=semantic_document.title,
        title_lang=semantic_document.dossier.lang,
        title_suffix=semantic_document.title_suffix,
        external_semantic_document_id=semantic_document.external_semantic_document_id,
        document_category_id=semantic_document.document_category.id,
        document_category_key=semantic_document.document_category.name,
        document_category_title_de=semantic_document.document_category.de,
        document_category_title_en=semantic_document.document_category.en,
        document_category_title_fr=semantic_document.document_category.fr,
        document_category_title_it=semantic_document.document_category.it,
        created_at=semantic_document.created_at,
        updated_at=semantic_document.updated_at,
        last_change=get_semantic_document_last_change(semantic_document),
        access_mode=semantic_document.access_mode,
        custom_attribute=semantic_document.custom_attribute,
        semantic_pages=[
            schemas.SemanticPage(
                uuid=page.uuid,
                number=page.number,
                image_url=page.processed_page.image.fast_url,
                updated_at=page.updated_at,
            )
            for page in semantic_document.semantic_pages.all()
        ],
    )


@api.get(
    "/document-categories",
    response={200: schemas.DocumentCategories},
    url_name="document-categories",
)
def get_document_categories(request):
    account_key = request.auth.account_key

    return schemas.DocumentCategories(
        root={
            category.name: schemas.DocumentCategory(
                key=category.name,
                id=category.id,
                title_de=category.de,
                title_en=category.en or "",
                title_fr=category.fr or "",
                title_it=category.it or "",
                description_de=category.description_de,
                description_en=category.description_en,
                description_fr=category.description_fr,
                description_it=category.description_it,
            )
            for category in dossier_models.DocumentCategory.objects.filter(
                account__key=account_key
            ).all()
        }
    )


@api.post(
    "/export/dossier/",
    response={200: schemas.ExportRequest},
    url_name="export-dossier-export",
)
def export_dossier_export(request, dossier_export_dossier: schemas.ExportDossierExport):
    """Export a dossier as a zip file

    For an external dossier, dispatch a request to rabbitmq for dossier export and return a download url

    If a dossier export already exists, create a new dossier export and return a download url
    """

    # we rather want something like the zip file created by the
    # dossier zipper component which is used in our core app
    # Exports are asynchronous, and take some time, so we
    # first request and export and then poll for the status
    # There may be changes as we are not sure whether they work with batches (multiple dossiers)

    dossier_user: DossierUser = request.auth.get_user_or_create()

    dossier: Dossier = get_dossier_with_access_check(
        dossier_user=dossier_user,
        is_manager=True,  # Fix this to do proper ownership
        external_dossier_id=dossier_export_dossier.external_dossier_id,
    )

    if OriginalFile.objects.filter(dossier=dossier).first() is None:
        raise HttpError(
            400,
            f"Dossier {dossier.uuid} with external id {dossier.external_id} does not have any files",
        )

    dossier_zip_request: DossierZipRequestV1 = generate_dossier_zip_request(
        dossier=dossier, add_uuid_suffix=dossier_export_dossier.add_uuid_suffix
    )

    publish(
        message=dossier_zip_request.model_dump_json().encode(),
        routing_key=ASYNC_DOSSIER_ZIPPER_WORKER_V2_ROUTING_KEY,
    )

    return schemas.ExportRequest(export_uuid=dossier_zip_request.zip_request_uuid)


@api.get(
    "/export/dossier/{export_uuid}/status",
    response=schemas.ExportStatus,
    url_name="dossier-export-status",
)
def get_dossier_export_status(request, export_uuid):
    # Check the status of an individual document

    account: Account = get_object_or_404(Account, key=request.auth.account_key)

    export_dossier: DossierExport = get_object_or_404(DossierExport, uuid=export_uuid)

    # Check that user has access to an associated account
    if export_dossier.dossier.account != account:
        raise Http404("No permission to access this dossier")

    status = schemas.ExportProcessingStatus.PROCESSING
    if export_dossier.done:
        status = schemas.ExportProcessingStatus.PROCESSED

    return schemas.ExportStatus(
        export_uuid=export_uuid,
        status=status,
        dossier_url=export_dossier.file.get_fast_url(),
        updated_at=export_dossier.done,
    ).model_dump()


@api.delete(
    "/dossier/{external_dossier_id}", response={200: Message}, url_name="dossier-delete"
)
def delete_dossier(request, external_dossier_id: str):
    dossier = get_dossier_with_access_check(
        dossier_user=request.auth.get_user_or_create(),
        is_manager=True,  # Fix this to do proper ownership
        external_dossier_id=external_dossier_id,
    )

    dossier.expiry_date = timezone.now() - timedelta(
        days=1
    )  # Delete function in dossier service is not fully implemented
    # The idea is to delete the file data but keep the dossier (dispatch task to delete from s3)
    dossier.save()

    return 200, {"detail": "Dossier Scheduled for deletion"}


@api.post(
    "/export/dossier/{external_dossier_id}/semantic-documents/{semantic_document_uuid}",
    response={200: schemas.SemanticDocumentPDFExportRequest, 404: Message},
    url_name="export-dossier-semantic-document-pdf",
)
def export_semantic_document_pdf(
    request,
    external_dossier_id,
    semantic_document_uuid,
):
    request: SemanticDocumentPDFRequestV1 = (
        export_semantic_document_wrapper_with_access_check(
            dossier_user=request.auth.get_user_or_create(),
            external_dossier_id=external_dossier_id,
            semantic_document_uuid=semantic_document_uuid,
        )
    )

    publish(
        message=request.model_dump_json().encode(),
        routing_key=ASYNC_DOSSIER_SEMANTIC_DOCUMENT_PDF_WORKER_ROUTING_KEY,
    )

    return SemanticDocumentPDFExportRequest(
        uuid=request.semantic_document_pdf_request_uuid
    )


@api.get(
    "/export/{semantic_document_export_request_uuid}/status",
    response=schemas.ExportStatus,
    url_name="dossier-semantic-document-export-status",
    exclude_none=True,
)
def get_semantic_document_export_status(
    request, semantic_document_export_request_uuid: str
):
    # Check the status of an individual semantic document's export status

    dossier_user = request.auth.get_user_or_create()
    account = dossier_user.account

    export_semantic_document: SemanticDocumentExport = get_object_or_404(
        SemanticDocumentExport, uuid=semantic_document_export_request_uuid
    )

    # Check that user has access to an associated account
    if export_semantic_document.semantic_document.dossier.account != account:
        raise Http404("No permission to access this dossier")

    status = schemas.ExportProcessingStatus.PROCESSING
    dossier_url = None
    if export_semantic_document.done:
        status = schemas.ExportProcessingStatus.PROCESSED
        dossier_url = export_semantic_document.file.get_fast_url()

    return schemas.ExportStatus(
        export_uuid=semantic_document_export_request_uuid,
        status=status,
        dossier_url=dossier_url,
        updated_at=export_semantic_document.done,
    )


@api.delete(
    "/dossier/{external_dossier_id}/semantic-document/{semantic_document_uuid}",
    response={200: semantic_document_schemas.SavingResultWithMessage},
    url_name="semantic-document-soft-delete",
)
def soft_delete_semantic_document(
    request, external_dossier_id: str, semantic_document_uuid: UUID
):
    # TODO: Talk with Andreas about this
    # If he's happy with this, combine this dossier access check with
    # semantic_restore_or_delete, in local helpers
    # as auth checks are different to semantic_restore_or_delete_with_access_check
    dossier = get_dossier_with_access_check(
        dossier_user=request.auth.get_user_or_create(),
        is_manager=True,  # Fix this to do proper ownership
        external_dossier_id=external_dossier_id,
    )

    semantic_document_helpers.semantic_restore_or_delete(
        dossier,
        semantic_document_models.SemanticDocument.all_objects,
        semantic_document_uuid,
        True,
    )

    return semantic_document_schemas.SavingResultWithMessage(message="deleted")


@api.delete(
    "/dossier/{external_dossier_id}/semantic-page/{semantic_page_uuid}",
    response={200: semantic_document_schemas.SavingResultWithMessage},
    url_name="semantic-page-soft-delete",
)
def soft_delete_semantic_page(
    request, external_dossier_id: str, semantic_page_uuid: UUID
):
    dossier = get_dossier_with_access_check(
        dossier_user=request.auth.get_user_or_create(),
        is_manager=True,  # Fix this to do proper ownership
        external_dossier_id=external_dossier_id,
    )

    semantic_document_helpers.semantic_restore_or_delete(
        dossier,
        semantic_document_models.SemanticPage.all_objects,
        semantic_page_uuid,
        True,
    )

    return semantic_document_schemas.SavingResultWithMessage(message="deleted")


@api.put(
    "/dossier/{external_dossier_id}/semantic-document/{semantic_document_uuid}/restore",
    response={200: semantic_document_schemas.SavingResultWithMessage},
    url_name="semantic-document-restore",
)
def undelete_semantic_document(
    request, external_dossier_id: str, semantic_document_uuid: UUID
):
    dossier = get_dossier_with_access_check(
        dossier_user=request.auth.get_user_or_create(),
        is_manager=True,  # Fix this to do proper ownership
        external_dossier_id=external_dossier_id,
    )

    semantic_document_helpers.semantic_restore_or_delete(
        dossier,
        semantic_document_models.SemanticDocument.all_objects,
        semantic_document_uuid,
        False,
    )

    return semantic_document_schemas.SavingResultWithMessage(message="restored")


@api.put(
    "/dossier/{external_dossier_id}/semantic-page/{semantic_page_uuid}/restore",
    response={200: semantic_document_schemas.SavingResultWithMessage},
    url_name="semantic-page-restore",
)
def undelete_semantic_page(request, external_dossier_id: str, semantic_page_uuid: UUID):
    dossier = get_dossier_with_access_check(
        dossier_user=request.auth.get_user_or_create(),
        is_manager=True,  # Fix this to do proper ownership
        external_dossier_id=external_dossier_id,
    )

    semantic_document_helpers.semantic_restore_or_delete(
        dossier,
        semantic_document_models.SemanticPage.all_objects,
        semantic_page_uuid,
        False,
    )

    return semantic_document_schemas.SavingResultWithMessage(message="restored")


# @api.get("/access-check", response={200: schemas.DossierAuthorization})
# def check_access(request, dossier_access_request: schemas.DossierAccessRequest):
#     """ stub for authorization service for discussion"""
