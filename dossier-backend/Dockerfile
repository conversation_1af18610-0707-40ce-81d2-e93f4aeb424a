FROM python:3.10

ARG YOUR_ENV


RUN apt update && apt install -y openssh-server postgresql-client poppler-utils

# see also https://izziswift.com/integrating-python-poetry-with-docker/
ENV YOUR_ENV=${YOUR_ENV} \
  PYTHONFAULTHANDLER=1 \
  PYTHONUNBUFFERED=1 \
  PYTHONHASHSEED=random \
  PIP_NO_CACHE_DIR=off \
  PIP_DISABLE_PIP_VERSION_CHECK=on \
  PIP_DEFAULT_TIMEOUT=100 \
  POETRY_VERSION=2.1.2 \
  POETRY_CACHE_DIR='/var/cache/pypoetry'


#  \
#  OPENBLAS_NUM_THREADS=1

# Make sure pip is updated:
RUN python3 -m ensurepip --upgrade && pip install --upgrade pip setuptools wheel
RUN pip install --no-cache-dir "poetry==$POETRY_VERSION"


RUN mkdir /app
WORKDIR /app
COPY poetry.lock pyproject.toml /app/



# Project initialization:
RUN --mount=type=secret,id=auth_toml,dst=/root/.config/pypoetry/auth.toml poetry config virtualenvs.create false \
  && poetry install --no-interaction --no-ansi && rm -rf $POETRY_CACHE_DIR

# Creating folders, and files for a project:
COPY . /app
#RUN sh check_vulnerabilities.sh
RUN chmod +x ./start.sh
EXPOSE 8000
CMD ["./start.sh"]
