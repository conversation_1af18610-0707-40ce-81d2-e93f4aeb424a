import asyncio
import concurrent
import functools
import io
import os
from uuid import UUID
import platform

import aio_pika
from aio_pika.patterns import RPC
from asgiref.sync import sync_to_async
from django.db.models import F, Q
from django.shortcuts import get_object_or_404


from dossier.models import Dossier, Languages
from image_exporter import schemas as image_exporter_schemas
from django.conf import settings
from semantic_document.models import SemanticPagePageObject, SemanticDocument
from ninja.errors import HttpError

from dossier import helpers as dossier_helpers
from core.helpers import remove_invalid_chars

PAGE_OBJECT_IMAGE_TYPE = "IMAGE"
executor = concurrent.futures.ThreadPoolExecutor()


def get_default_filter(dossier_uuid: UUID):
    return Q(
        semantic_page__dossier__uuid=dossier_uuid,
        semantic_page__deleted_at=None,
        semantic_page__semantic_document__deleted_at=None,
        page_object__visible=True,
    )


def create_filter_for_page_object_image(
    dossier_uuid: UUID,
    page_object_filters: image_exporter_schemas.ExportPageImageRequestBody.model_dump,
):
    filter_q = get_default_filter(dossier_uuid)

    checkbox_filters = Q()

    export_to_sql = {
        "export_photos": "photo_",
        "export_plans": "plan_",
        "export_misc_images": "misc_",
        "export_identity_documents": "identity_",
    }

    for key in page_object_filters:
        if page_object_filters.get(key) and export_to_sql.get(key):
            checkbox_filters |= Q(page_object__key__de__startswith=export_to_sql[key])

    filter_q = filter_q & checkbox_filters

    return filter_q


def get_page_object_query_set(
    dossier_uuid: UUID,
    page_object_filters: image_exporter_schemas.ExportPageImageRequestBody.model_dump,
):
    filter_q = create_filter_for_page_object_image(dossier_uuid, page_object_filters)

    return (
        SemanticPagePageObject.objects.select_related(
            "page_object",
            "semantic_page",
            "page_object__key",
            "page_object__type",
            "page_object__processed_page",
            "semantic_page__semantic_document",
            "page_object__processed_page__image",
        )
        .filter(filter_q)
        .annotate(
            document_title=F("semantic_page__semantic_document__title_custom"),
            semantic_page_number=F("semantic_page__number"),
            rotation=F("semantic_page__rotation_angle"),
        )
        .order_by("document_title", "created_at")
    )


def get_serialized_page_object(
    dossier_uuid: UUID,
    page_object_filters: image_exporter_schemas.ExportPageImageRequestBody.model_dump,
):
    queryset_of_page_objects = get_page_object_query_set(
        dossier_uuid, page_object_filters
    )
    dossier = Dossier.objects.get(uuid=dossier_uuid)

    return [
        {
            **dossier_helpers.prepare_page_object(
                queryset_of_page_objects[page_object_index].page_object
            ),
            "original_filename": queryset_of_page_objects[
                page_object_index
            ].page_object.processed_page.image.data.name,
            "url": queryset_of_page_objects[
                page_object_index
            ].page_object.processed_page.image.get_fast_url(),
            "formatted_title": SemanticDocument.calculate_title(
                dossier.lang.lower(),
                queryset_of_page_objects[
                    page_object_index
                ].semantic_page.semantic_document.title_custom,
                queryset_of_page_objects[
                    page_object_index
                ].semantic_page.semantic_document.document_category,
                queryset_of_page_objects[
                    page_object_index
                ].semantic_page.semantic_document.title_suffix,
            ),
            "semantic_page_number": queryset_of_page_objects[
                page_object_index
            ].semantic_page_number,
            "page_object_index": page_object_index,
            "rotation": queryset_of_page_objects[page_object_index].rotation,
            "semantic_document_uuid": queryset_of_page_objects[
                page_object_index
            ].semantic_page.semantic_document.uuid,
            "semantic_page_uuid": queryset_of_page_objects[
                page_object_index
            ].semantic_page.uuid,
            "semantic_page_deleted_at": queryset_of_page_objects[
                page_object_index
            ].semantic_page.deleted_at,
        }
        for page_object_index in range(len(queryset_of_page_objects))
    ]


def get_page_objects(
    dossier_uuid: UUID,
    page_object_filters: image_exporter_schemas.ExportPageImageRequestBody.dict,
):
    return get_serialized_page_object(dossier_uuid, page_object_filters)


def generate_name_for_page_objects_images_zip_archive(
    dosser: Dossier, add_photo_album_docx: bool
):
    if add_photo_album_docx:
        second_part_of_archive_name = (
            "Fotoalbum.zip" if dosser.lang == Languages.GERMAN else "Photo Album.zip"
        )
    else:
        second_part_of_archive_name = (
            "Bilder.zip" if dosser.lang == Languages.GERMAN else "Images.zip"
        )
    return f"{remove_invalid_chars(dosser.name)} {second_part_of_archive_name}"


async def generate_images(
    dossier_uuid: UUID, request_data: image_exporter_schemas.ExportPageImageRequestBody
) -> image_exporter_schemas.ExportPageObjectsResponse:
    dossier = await sync_to_async(get_object_or_404)(Dossier, uuid=dossier_uuid)

    add_photo_album_docx = (
        request_data.export_photos
        and not request_data.export_plans
        and not request_data.export_misc_images
        and not request_data.export_identity_documents
    )

    archive_name = generate_name_for_page_objects_images_zip_archive(
        dossier, add_photo_album_docx
    )

    dossier_file = dossier_helpers.create_dossier_file_without_saving(
        dossier, archive_name
    )
    await sync_to_async(dossier_file.save)()

    dossier_export_response = await call_rabbit(
        dossier_uuid,
        dossier_file.put_url,
        archive_name,
        request_data,
        add_photo_album_docx,
    )
    validate_image_export_response(dossier_export_response)

    return image_exporter_schemas.ExportPageObjectsResponse(
        location=dossier_file.fast_url,
        count_page_objects=dossier_export_response.payload.count_page_objects,
        name_of_zip_archive=archive_name,
    )


async def call_rabbit(
    dossier_uuid: UUID,
    put_upload_url: str,
    name_of_archive: str,
    page_object_filters: image_exporter_schemas.ExportPageImageRequestBody,
    add_photo_album_docx: bool,
) -> image_exporter_schemas.ImageExportResponseV1:
    connection = await aio_pika.connect_robust(
        f"{settings.RABBIT_URL}?name=image exporter {platform.node()}",
        client_properties={"connection_name": "caller"},
    )

    request_data = image_exporter_schemas.ImageExportRequestV1(
        dossier_uuid=str(dossier_uuid),
        put_upload_url=str(put_upload_url),
        name_of_archive=str(name_of_archive),
        page_object_filters=page_object_filters,
        add_photo_album_docx=add_photo_album_docx,
    )

    async with connection:
        channel = await connection.channel()
        rpc = await RPC.create(channel)

        dossier_response_str = await rpc.call(
            settings.ASYNC_IMAGE_EXPORTER_WORKER_V1_QUEUE_NAME,
            request_data.model_dump(),
        )

        return image_exporter_schemas.ImageExportResponseV1.model_validate_json(
            dossier_response_str
        )


def aio(f):
    """creates an async call from a sync call in a background thread"""

    async def aio_wrapper(*args, **kwargs):
        f_bound = functools.partial(f, *args, **kwargs)
        loop = asyncio.get_running_loop()
        return await loop.run_in_executor(executor, f_bound)

    return aio_wrapper


def find_percentage_of_number(num, per) -> int:
    return (per / 100) * num


def get_percentage_of_number(num, per):
    return (num / per) * 100


def calculate_side(original_value, side_value, relation_value):
    return find_percentage_of_number(
        original_value, get_percentage_of_number(relation_value, side_value)
    )


def get_file_extension(filepath: str) -> str:
    filename, file_extension = os.path.splitext(filepath)
    return file_extension


def get_confidence_value_up_to_100(confidence_value: float) -> int:
    return int(confidence_value * 100)


def generate_name_of_page_object(page_object, page_object_index: int):
    confidence_value = get_confidence_value_up_to_100(page_object["confidence"])

    return (
        f"{page_object['formatted_title']} "
        f"{number_to_double_digit_based(page_object_index - 1)} "
        f"{page_object['title']} "
        f"{number_to_double_digit_based(page_object['semantic_page_number'])} "
        f"{confidence_value}"
    )


def image_to_byte(image, file_extension) -> io.BytesIO:
    byte_io = io.BytesIO()

    format_file = (
        "JPEG" if file_extension[1:].lower() == "jpg" else file_extension.upper()
    )
    image.save(byte_io, format_file)

    return byte_io


def crop_image(image, bbox):
    width, height = image.size

    left = calculate_side(width, bbox["ref_width"], bbox["left"])
    top = calculate_side(height, bbox["ref_height"], bbox["top"])
    right = calculate_side(width, bbox["ref_width"], bbox["right"])
    bottom = calculate_side(height, bbox["ref_height"], bbox["bottom"])

    return image.crop((left, top, right, bottom))


def validate_image_export_response(
    response: image_exporter_schemas.ImageExportResponseV1,
):
    if response.http_status_code >= 400:
        raise HttpError(response.http_status_code, response.message)


def number_to_double_digit_based(input_number: int, min_chars=1) -> str:
    """
    PAGE_NUMBER_AS_DOUBLE_DIGIT_N_BASED
    e.g. input_number=0, min_chars=1 -> '01';
    e.g. input_number=16, min_chars=1 -> '17';
    e.g. input_number=0, min_chars=1 -> '001';
    e.g. input_number=16, min_chars=2 -> '017';
    """
    if min_chars <= 0:
        raise Exception("The min_chars can't be less than zero.")

    magic_number = (100 * 10 ** (min_chars - 1)) + 1
    if len(str(input_number)) >= len(str(magic_number)):
        return str(input_number + 1)

    return str(input_number + magic_number)[1:]
