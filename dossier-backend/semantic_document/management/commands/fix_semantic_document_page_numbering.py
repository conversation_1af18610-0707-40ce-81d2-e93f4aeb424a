import djclick as click
from django.db import transaction
import logging

from semantic_document.models import SemanticDocument, SemanticPage
from semantic_document.helpers import fix_document_page_numbering

logger = logging.getLogger(__name__)


@click.command()
@click.argument("document_uuid")
@click.option(
    "--dry-run",
    "-d",
    default=True,
    help="Show what would be done without making actual changes",
)
@click.option(
    "--fix-non-sequential-pages",
    "-p",
    default=False,
    help="Whether to also handle fixing non-sequential pages",
)
def fix_semantic_document_page_numbering(
    document_uuid: str, dry_run: bool, fix_non_sequential_pages: bool
):
    """
    Fix page numbering for a specific semantic document.

    Example usage:

    # Dry run to see what would change
    python manage.py fix_semantic_document_page_numbering <document-uuid>

    # Actually fix the document
    python manage.py fix_semantic_document_page_numbering <document-uuid> --dry-run False
    """
    try:
        doc = SemanticDocument.objects.get(uuid=document_uuid)
    except SemanticDocument.DoesNotExist:
        logger.error(f"Document with UUID '{document_uuid}' not found")
        return

    logger.info(
        f"Starting page number fix for document '{document_uuid}' "
        f"(dry_run={dry_run})"
    )

    try:
        with transaction.atomic():
            # Get current page numbers for logging
            pages = SemanticPage.all_objects.filter(semantic_document=doc).order_by(
                "number", "deleted_at", "-updated_at"
            )
            original_numbers = list(pages.values_list("number", flat=True))

            if dry_run:
                logger.info(
                    f"Would fix document {doc.uuid}: "
                    f"pages {original_numbers} -> "
                    f"starting from 0"
                )
            else:
                if fix_document_page_numbering(
                    doc, fix_non_sequential_pages=fix_non_sequential_pages
                ):
                    logger.info(
                        f"Fixed document {doc.uuid}: "
                        f"pages {original_numbers} -> "
                        f"starting from 0"
                    )
                else:
                    logger.warning(
                        f"Could not fix document {doc.uuid}: " "not suitable for fixing"
                    )

    except Exception as e:
        logger.error(f"Error processing document {doc.uuid}: {str(e)}", exc_info=True)

    if dry_run:
        logger.info(
            "This was a dry run - no changes were made. "
            "Run with '--dry-run=False' to apply changes"
        )
