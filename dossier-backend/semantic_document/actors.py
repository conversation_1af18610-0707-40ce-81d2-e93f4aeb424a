from pathlib import Path
from tempfile import TemporaryDirectory
from typing import Union
import pika

import structlog
from asgiref.sync import sync_to_async
from django.conf import settings
from pydantic import HttpUrl, StrBytes

from core.publisher import publish
from semantic_document.schemas import (
    SemanticDocumentPDFRequestV2,
    SemanticDocumentPDFResponseV2,
)
from tempfilestore.filestore import (
    upload_file_and_get_presigned_url,
)

from workers.services.semantic_document_pdf_export import (
    worker_generate_semantic_document_pdf,
)

logger = structlog.get_logger(__name__)


async def process_semantic_document_pdf_tempfile_storage(
    *, semantic_document_pdf_request: Union[str, StrBytes, SemanticDocumentPDFRequestV2]
):
    """
    Handle accepting a semantic dossier pdf request from rabbitmq and returning the pdf
    @param semantic_document_pdf_request:
    @return: Publish to dossier event consumer to save to django db

    Version 2 of process_semantic_dossier_pdf_request
    Wrapper around dispatch_rpc_request to specifically handle generating semantic document single pdf

    Do the work based off process_semantic_dossier_pdf_request, but use temporary filestore

    publish to new worker to save to a file in django db, save_semantic_document_pdf_v2_completion
    inside dossier event consumer
    Publish to

    """
    try:
        if isinstance(semantic_document_pdf_request, str) or isinstance(
            semantic_document_pdf_request, StrBytes
        ):
            semantic_document_pdf_request = (
                SemanticDocumentPDFRequestV2.model_validate_json(
                    semantic_document_pdf_request
                )
            )
        elif isinstance(semantic_document_pdf_request, dict):
            print(semantic_document_pdf_request)
            semantic_document_pdf_request = SemanticDocumentPDFRequestV2(
                **semantic_document_pdf_request
            )
        else:
            semantic_document_pdf_request = semantic_document_pdf_request

        logger.info(
            f"processing semantic document pdf request for temporary file storage "
            f"{semantic_document_pdf_request.semantic_document_pdf_request_uuid}"
        )

        try:
            with TemporaryDirectory() as temp_dir:
                temp_path: Path = Path(temp_dir)

                await sync_to_async(worker_generate_semantic_document_pdf)(
                    semantic_dossier=semantic_document_pdf_request.semantic_dossier,
                    semantic_document_uuid=semantic_document_pdf_request.semantic_document_uuid,
                    dest_path=temp_path,
                    add_metadata=semantic_document_pdf_request.add_metadata,
                    add_uuid_suffix=semantic_document_pdf_request.add_uuid_suffix,
                )

                res: HttpUrl = await upload_file_and_get_presigned_url(temp_path)

                publish(
                    message=SemanticDocumentPDFResponseV2(
                        semantic_document_pdf_request_uuid=semantic_document_pdf_request.semantic_document_pdf_request_uuid,
                        temp_upload_url=res,
                    )
                    .json()
                    .encode(),
                    routing_key=settings.ASYNC_DOSSIER_EVENTS_WORKER_V1_QUEUE_NAME,
                    properties=pika.BasicProperties(
                        type="SemanticDocumentEvent.SemanticDocumentPDFResponseV1",
                    ),
                )

        except Exception as e:
            logger.exception(
                "could not create offline package for semantic_document_pdf_request",
                request_uuid=semantic_document_pdf_request.semantic_document_pdf_request_uuid,
            )
            raise e

        logger.info(
            "finished processing semantic_document_pdf_request request",
            request_uuid=semantic_document_pdf_request.semantic_document_pdf_request_uuid,
        )
        # publish has a reply to, where the function return is passed in as the last argument to an actor
        # publish to new worker to save to a file in django db, save_semantic_document_pdf_v2_completion
        # set message.type = SemanticDocumentEvent.SemanticDocumentPDFResponseV1
        return SemanticDocumentPDFResponseV2(
            semantic_document_pdf_request_uuid=semantic_document_pdf_request.semantic_document_pdf_request_uuid,
            temp_upload_url=res,
        ).json()
    except Exception:
        logger.exception("could not process message", exc_info=True)
