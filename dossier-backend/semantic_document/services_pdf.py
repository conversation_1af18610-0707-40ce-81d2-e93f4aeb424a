import json
import tempfile
from io import Bytes<PERSON>
from pathlib import Path
from typing import Optional, Sequence, Callable, List

import PyPDF2
import requests
import structlog
from PyPDF2 import PdfWriter, PdfReader
from reportlab.pdfgen import canvas
from PyPDF2.generic import (
    DictionaryObject,
    NameObject,
    ArrayObject,
    NumberObject,
    FloatObject,
)
from pydantic import BaseModel
from reportlab.platypus.paragraph import Paragraph
from reportlab.lib.styles import ParagraphStyle

from semantic_document.pdf_metadata_enhanced import add_enhanced_metadata_to_pdf
from semantic_document.pdf_styles import (
    create_test_style,
    create_optimal_style,
    create_comment_style,
)

from dossier.doc_cat_helpers import get_document_categories_by_name
from dossier.schemas import SemanticDocumentFullApiData
from dossier_zipper.helpers import rotate_pdf
from semantic_document.schemas_page_annotations import (
    AnnotationType,
    UserAnnotationsSchema,
)

logger = structlog.get_logger()

HYPODOSSIER_METADATA_SCHEMA_VERSION = "1.0.0"


class PdfSemanticDocumentMetadata(BaseModel):
    version: str
    uuid: str
    title: str
    document_category_key: str
    document_category_name: str
    document_category_title_de: str
    document_category_title_en: str
    document_category_title_fr: str
    document_category_title_it: str
    title_suffix: Optional[str] = None
    # filename: str
    # confidence: float
    # creator: str
    # producer: str


def create_document_info_properties(data: PdfSemanticDocumentMetadata):
    # Create full title with suffix if available
    full_title = data.title
    if data.title_suffix:
        full_title = f"{data.title} {data.title_suffix}"

    return {
        "/Title": full_title,
        "/Author": "",
        "/Subject": data.title,  # Base title without suffix
        "/DocumentCategoryKey": data.document_category_key,
        "/TitleSuffix": data.title_suffix,
        "/Creator": "HypoDossier AG",
        "/Producer": "Dossier Manager 1.0",
    }


def add_document_info_to_pdfwriter(
    writer: PdfWriter, document: SemanticDocumentFullApiData
):
    m = create_pdf_semantic_document_metadata(document)
    d = create_document_info_properties(m)
    writer.add_metadata(d)


def add_metadata_attachment_to_pdfwriter(
    writer: PdfWriter, document: SemanticDocumentFullApiData
):
    m = create_pdf_semantic_document_metadata(document)

    with tempfile.TemporaryDirectory() as temp_dir:
        metafile_path = Path(temp_dir) / "HypoDossierData.json"
        with open(metafile_path, "w") as f:
            j = json.dumps(m.model_dump(), indent=4)
            f.write(j)

        with open(metafile_path, "rb") as f:
            # txt_data = b'asdfasdfasdf'
            txt_data = f.read()
            writer.add_attachment(metafile_path.name, txt_data)


def create_pdf_semantic_document_metadata(document: SemanticDocumentFullApiData):
    document_categories = get_document_categories_by_name()
    doc_cat_name = document.document_category.name
    doc_cat = document_categories[doc_cat_name]
    title = (
        document.formatted_title
        if hasattr(document, "formatted_title")
        else document.title
    )

    # Handle both SemanticDocumentFullApiData (with 'suffix') and SemanticDocument model (with 'title_suffix')
    title_suffix = None
    if hasattr(document, "suffix"):
        title_suffix = document.suffix
    elif hasattr(document, "title_suffix"):
        title_suffix = document.title_suffix

    return PdfSemanticDocumentMetadata(
        version=HYPODOSSIER_METADATA_SCHEMA_VERSION,
        title=title,
        document_category_key=document.document_category.name,
        document_category_name=document.document_category.name,
        document_category_title_de=doc_cat.translated("de"),
        document_category_title_en=doc_cat.translated("en"),
        document_category_title_fr=doc_cat.translated("fr"),
        document_category_title_it=doc_cat.translated("it"),
        title_suffix=title_suffix,
        uuid=str(document.uuid),
    )


def hex_to_rgb_float(hex_color: str):
    """
    Convert a hexadecimal color string to RGB float tuple (R, G, B).
    Each value will be between 0 and 1.
    """
    hex_color = hex_color.lstrip("#")
    lv = len(hex_color)
    if lv != 6:
        raise ValueError(
            "Incorrect hex color format. Use 6-digit format without '#' prefix."
        )
    return tuple(int(hex_color[i : i + 2], 16) / 255.0 for i in range(0, lv, 2))


def normalize_newlines(text: str) -> str:
    """
    Normalize different types of newlines to a consistent format.

    Args:
        text: The text to normalize

    Returns:
        str: Text with normalized newlines
    """
    if not text:
        return ""
    return text.replace("\r\n", "\n").replace("\r", "\n").strip()


def prepare_text_for_reportlab(text: str) -> str:
    if not text:
        return ""
    else:
        t1 = normalize_newlines(text)
        t2 = t1.replace("\n", "<br/>")
        return t2


def check_text_overflow(
    text: str, frame_width: float, frame_height: float, style: ParagraphStyle
) -> bool:
    """
    Check if text will overflow the given frame dimensions.

    Args:
        text: The text to check
        frame_width: Width of the frame in points
        frame_height: Height of the frame in points
        style: ParagraphStyle to use for rendering

    Returns:
        bool: True if text overflows, False otherwise
    """
    if not text:
        return False

    # Normalize newlines before processing
    text = prepare_text_for_reportlab(text)

    # Ensure style has proportional leading if not already set
    if not hasattr(style, "leading") or style.leading is None:
        style.leading = style.fontSize * 1.2

    # Create paragraph with the text
    p = Paragraph(text, style)

    # Get the space required by the paragraph
    needed_width, needed_height = p.wrap(frame_width, frame_height)

    # If needed_height is zero, text couldn't be wrapped to fit width
    if needed_height == 0:
        return True

    # Check if text needs more height than available
    if needed_height > frame_height:
        return True

    return False


def find_optimal_font_size(
    text: str, frame_width: float, frame_height: float, base_style: ParagraphStyle
) -> float:
    """
    Find the optimal font size that allows text to fit within the given frame dimensions.
    Uses binary search to efficiently find the largest font size that fits.

    Args:
        text: The text to fit
        frame_width: Width of the frame in points
        frame_height: Height of the frame in points
        base_style: Base ParagraphStyle to use for rendering

    Returns:
        float: Optimal font size in points
    """
    if not text:
        return base_style.fontSize

    # Normalize newlines before processing
    text = prepare_text_for_reportlab(text)

    # Define search range for font sizes

    # Minimum readable font size. PDF can be zoomed so even this small it is readable if zoomed in
    min_font_size = 2.0

    # Maximum font size to try - have at max a single line with 80% of the heigt of the box
    max_font_size = max(frame_height * 0.9, 18)

    # Default to minimum if nothing fits
    optimal_font_size = min_font_size

    # Binary search for the optimal font size
    while max_font_size - min_font_size > 0.5:  # Continue until precision is 0.5 points
        mid_font_size = (min_font_size + max_font_size) / 2

        # Create a new style with the current font size and proportional leading
        test_style = create_test_style(base_style=base_style, font_size=mid_font_size)

        # Check if text fits with this font size
        p = Paragraph(text, test_style)
        needed_width, needed_height = p.wrap(frame_width, frame_height)

        if frame_height >= needed_height > 0 and needed_width <= frame_width:
            # This font size fits, try a larger one
            min_font_size = mid_font_size
            optimal_font_size = mid_font_size
        else:
            # This font size is too large, try a smaller one
            max_font_size = mid_font_size

    # Return the largest font size that fits
    return optimal_font_size


def add_annotations_to_page(
    page,
    annotations: List[UserAnnotationsSchema],
    writer: PyPDF2.PdfWriter,
    highlight_opacity: float = 0.5,
):
    """
    Helper function to add annotations to a PDF page
    @param page: PDF page object
    @param annotations: Pydantic model containing list of annotations
    @param writer: PDF writer instance
    @param highlight_opacity: Opacity level for highlights (0-1)
    """
    if not hasattr(page, "annotations"):
        page[NameObject("/Annots")] = ArrayObject()

    for annotation in annotations:
        page_height = float(page.mediabox.height)
        page_width = float(page.mediabox.width)

        # Scale bbox values to absolute coordinates
        abs_top = annotation.bbox_top * page_height
        abs_height = annotation.bbox_height * page_height
        abs_left = annotation.bbox_left * page_width
        abs_width = annotation.bbox_width * page_width

        # PDF coordinate system with bottom-left origin:
        bottom = page_height - (abs_top + abs_height)
        top = page_height - abs_top
        left = abs_left
        right = abs_left + abs_width

        # Convert the hex color to RGB float values
        rgb_color = hex_to_rgb_float(annotation.hexcolor)

        if annotation.annotation_type == AnnotationType.HIGHLIGHT:
            highlight_dict = DictionaryObject()

            highlight_dict.update(
                {
                    NameObject("/Type"): NameObject("/Annot"),
                    NameObject("/Subtype"): NameObject("/Highlight"),
                    NameObject("/F"): NumberObject(4),
                    NameObject("/Rect"): ArrayObject(
                        [
                            FloatObject(left),
                            FloatObject(bottom),
                            FloatObject(right),
                            FloatObject(top),
                        ]
                    ),
                    NameObject("/QuadPoints"): ArrayObject(
                        [
                            FloatObject(left),
                            FloatObject(top),
                            FloatObject(right),
                            FloatObject(top),
                            FloatObject(left),
                            FloatObject(bottom),
                            FloatObject(right),
                            FloatObject(bottom),
                        ]
                    ),
                    NameObject("/C"): ArrayObject([FloatObject(c) for c in rgb_color]),
                    NameObject("/CA"): FloatObject(highlight_opacity),
                }
            )

            # Add the annotation directly to the page's annotations array
            if not isinstance(page.get("/Annots"), ArrayObject):
                page[NameObject("/Annots")] = ArrayObject()

            # Add the annotation to the writer and get a reference
            highlight_ref = writer._add_object(highlight_dict)
            page["/Annots"].append(highlight_ref)

        if annotation.annotation_type == AnnotationType.COMMENT:
            # Create a rectangle overlay with text using reportlab
            packet = BytesIO()

            # Set up text rendering style first
            style = create_comment_style()

            # Use minimal padding to reduce border size
            padding = abs_width * 0.01  # Add padding as a percentage of width
            frame_width = abs_width - (2 * padding)
            frame_height = abs_height - (2 * padding)

            overflow = check_text_overflow(
                annotation.text or "", frame_width, frame_height, style
            )

            # Current logic is that we resize the font so that the text fits, including decreasing or increasing it
            # We could do a check with if overflow, an only decrease size if there was an overflow.
            # That's why overflow is calculates and inital style.fontSize is set

            # Find the optimal font size that fits the text in the box
            optimal_font_size = find_optimal_font_size(
                annotation.text or "", frame_width, frame_height, style
            )

            # Create a style with the optimal font size
            style = create_optimal_style(base_style=style, font_size=optimal_font_size)

            # Log the font size adjustment
            logger.info(
                "Using optimal font size for comment box",
                annotation_group_uuid=annotation.annotation_group_uuid,
                font_size=optimal_font_size,
                leading=optimal_font_size
                * 1.2,  # Leading is calculated in the style class
                box_width=abs_width,
                box_height=abs_height,
                overflow=overflow,
            )

            # Create paragraph with automatic text wrapping and optimal font size
            text = prepare_text_for_reportlab(annotation.text)
            p = Paragraph(text, style)

            # First draw the background rectangle
            c = canvas.Canvas(packet, pagesize=(page_width, page_height))
            c.setFillColorRGB(*rgb_color)  # Use the annotation's hexcolor
            c.setFillAlpha(1)  # Full opacity for rectangle
            c.rect(left, bottom, abs_width, abs_height, fill=1, stroke=0)

            # Determine paragraph wrapping and actual height
            # Allow it to wrap to frame_width and take up to page_height vertically
            actual_paragraph_width, actual_paragraph_height = p.wrapOn(
                c, frame_width, page_height
            )

            # Draw the paragraph.
            # (left + padding) is the x-coordinate.
            # (top - actual_paragraph_height) is the y-coordinate for the bottom-left of the paragraph,
            # ensuring its top aligns with the 'top' of the comment box.
            p.drawOn(c, left + padding, top - actual_paragraph_height - padding)
            c.save()

            # Create PDF from the overlay
            packet.seek(0)
            overlay = PdfReader(packet)

            # Merge the overlay with the page
            page.merge_page(overlay.pages[0])


def create_semantic_document_pdf(
    semantic_document,
    pages: Sequence,
    get_pdf_url_func,
    get_annotations_func: Optional[Callable] = None,
    add_metadata: bool = True,
    writer: Optional[PyPDF2.PdfWriter] = None,
) -> PyPDF2.PdfWriter:
    if writer is None:
        writer = PyPDF2.PdfWriter()

    for page in pages:
        pdf_page_url = get_pdf_url_func(page)
        response = requests.get(pdf_page_url)
        pdf_reader = PyPDF2.PdfReader(BytesIO(response.content))
        pdf_page = pdf_reader.pages[0]
        rotation_angle = getattr(page, "rotation_angle", 0)

        # Apply rotation if needed
        rotated_page = rotate_pdf(pdf_page, rotation_angle)

        # Add annotations before adding the page to the writer
        if get_annotations_func:
            annotations = get_annotations_func(page)
            if annotations and len(annotations) > 0:
                add_annotations_to_page(rotated_page, annotations, writer)

        # Add the page to the writer
        writer.add_page(rotated_page)

    if add_metadata:
        # Add JSON attachment
        add_metadata_attachment_to_pdfwriter(writer, semantic_document)

    add_enhanced_metadata_to_pdf(
        writer,
        semantic_document,
    )

    filename = getattr(semantic_document, "filename", None) or semantic_document.title
    writer.add_outline_item(filename, 0)

    return writer
