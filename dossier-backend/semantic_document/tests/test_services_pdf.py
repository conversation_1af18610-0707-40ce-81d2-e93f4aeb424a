import pytest
import PyPDF2
from typing import List

from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from semantic_document.services_pdf import (
    add_annotations_to_page,
    check_text_overflow,
    find_optimal_font_size,
)
from semantic_document.schemas_page_annotations import (
    AnnotationType,
    UserAnnotationsSchema,
)


@pytest.fixture
def pdf_page():
    """Create a basic PDF page with A4 dimensions"""
    # Create a PDF writer and add a blank A4 page
    writer = PyPDF2.PdfWriter()
    # A4 dimensions in points (595.276 x 841.890)
    page = writer.add_blank_page(width=595.276, height=841.890)

    # Initialize empty annotations array if not present
    if "/Annots" not in page:
        from PyPDF2.generic import ArrayObject, NameObject

        page[NameObject("/Annots")] = ArrayObject()

    return page


@pytest.fixture
def pdf_writer():
    return PyPDF2.PdfWriter()


def test_add_highlight_annotation(pdf_page, pdf_writer):
    """Test adding a basic highlight annotation"""
    annotations = [
        UserAnnotationsSchema(
            annotation_type=AnnotationType.HIGHLIGHT,
            bbox_top=0.2,
            bbox_left=0.1,
            bbox_width=0.3,
            bbox_height=0.1,
            hexcolor="#FFFF00",  # Yellow
        )
    ]

    add_annotations_to_page(pdf_page, annotations, pdf_writer)

    # Verify the annotation was added
    assert len(pdf_page["/Annots"]) == 1
    annotation = pdf_page["/Annots"][0].get_object()
    assert annotation["/Subtype"] == "/Highlight"
    assert annotation["/QuadPoints"]  # Verify QuadPoints exists
    assert annotation["/C"] == [1, 1, 0]  # Yellow in RGB


def test_add_highlight_custom_color(pdf_page, pdf_writer):
    """Test adding a highlight with custom color"""
    annotations = [
        UserAnnotationsSchema(
            annotation_type=AnnotationType.HIGHLIGHT,
            bbox_top=0.2,
            bbox_left=0.1,
            bbox_width=0.3,
            bbox_height=0.1,
            hexcolor="#FF0000",  # Red
        )
    ]

    add_annotations_to_page(pdf_page, annotations, pdf_writer)

    highlight = pdf_page["/Annots"][0].get_object()
    color = [float(c) for c in highlight["/C"]]
    assert color == [1.0, 0.0, 0.0]  # RGB for red


def test_add_multiple_highlights(pdf_page, pdf_writer):
    """Test adding multiple highlights to the same page"""
    annotations = [
        UserAnnotationsSchema(
            annotation_type=AnnotationType.HIGHLIGHT,
            bbox_top=0.2,
            bbox_left=0.1,
            bbox_width=0.3,
            bbox_height=0.1,
            hexcolor="#FFFF00",
        ),
        UserAnnotationsSchema(
            annotation_type=AnnotationType.HIGHLIGHT,
            bbox_top=0.4,
            bbox_left=0.2,
            bbox_width=0.2,
            bbox_height=0.1,
            hexcolor="#00FF00",
        ),
    ]

    add_annotations_to_page(pdf_page, annotations, pdf_writer)

    assert len(pdf_page["/Annots"]) == 2

    # Verify positions are different
    highlight1 = pdf_page["/Annots"][0].get_object()
    highlight2 = pdf_page["/Annots"][1].get_object()
    assert highlight1["/Rect"] != highlight2["/Rect"]


def test_add_highlight_custom_opacity(pdf_page, pdf_writer):
    """Test adding a highlight with custom opacity"""
    annotations = [
        UserAnnotationsSchema(
            annotation_type=AnnotationType.HIGHLIGHT,
            bbox_top=0.2,
            bbox_left=0.1,
            bbox_width=0.3,
            bbox_height=0.1,
            hexcolor="#FFFF00",
        )
    ]

    custom_opacity = 0.8
    add_annotations_to_page(
        pdf_page, annotations, pdf_writer, highlight_opacity=custom_opacity
    )

    highlight = pdf_page["/Annots"][0].get_object()
    assert highlight["/CA"] == custom_opacity


def test_add_comment_annotation(pdf_page, pdf_writer):
    """Test adding a comment annotation"""
    annotations = [
        UserAnnotationsSchema(
            annotation_type=AnnotationType.COMMENT,
            bbox_top=0.2,
            bbox_left=0.1,
            bbox_width=0.3,
            bbox_height=0.1,
            text="Test comment",
        )
    ]

    add_annotations_to_page(pdf_page, annotations, pdf_writer)

    # Since comments are added as overlays, we need to verify the page was modified
    assert pdf_page.get_contents() is not None


def test_add_comment_long_text(pdf_page, pdf_writer):
    """Test adding a comment with long text that requires wrapping"""
    long_text = (
        "This is a very long comment that should be automatically wrapped "
        "to fit within the specified bounding box. The text should be "
        "properly formatted and visible in the PDF."
    )

    annotations = [
        UserAnnotationsSchema(
            annotation_type=AnnotationType.COMMENT,
            bbox_top=0.2,
            bbox_left=0.1,
            bbox_width=0.3,
            bbox_height=0.2,
            text=long_text,
        )
    ]

    add_annotations_to_page(pdf_page, annotations, pdf_writer)
    assert pdf_page.get_contents() is not None


def test_add_multiple_comments(pdf_page, pdf_writer):
    """Test adding multiple comments to the same page"""
    annotations = [
        UserAnnotationsSchema(
            annotation_type=AnnotationType.COMMENT,
            bbox_top=0.2,
            bbox_left=0.1,
            bbox_width=0.3,
            bbox_height=0.1,
            text="First comment",
        ),
        UserAnnotationsSchema(
            annotation_type=AnnotationType.COMMENT,
            bbox_top=0.4,
            bbox_left=0.2,
            bbox_width=0.2,
            bbox_height=0.1,
            text="Second comment",
        ),
    ]

    add_annotations_to_page(pdf_page, annotations, pdf_writer)
    assert pdf_page.get_contents() is not None


def test_add_mixed_annotations(pdf_page, pdf_writer):
    """Test adding both highlights and comments to the same page"""
    annotations = [
        UserAnnotationsSchema(
            annotation_type=AnnotationType.HIGHLIGHT,
            bbox_top=0.2,
            bbox_left=0.1,
            bbox_width=0.3,
            bbox_height=0.1,
            hexcolor="#FFFF00",
        ),
        UserAnnotationsSchema(
            annotation_type=AnnotationType.COMMENT,
            bbox_top=0.4,
            bbox_left=0.2,
            bbox_width=0.2,
            bbox_height=0.1,
            text="Test comment",
        ),
    ]

    add_annotations_to_page(pdf_page, annotations, pdf_writer)

    # Verify highlight annotation
    assert "/Annots" in pdf_page
    assert len(pdf_page["/Annots"]) == 1

    # Verify comment overlay
    assert pdf_page.get_contents() is not None


def test_empty_annotations(pdf_page, pdf_writer):
    """Test adding empty annotations list"""
    annotations: List[UserAnnotationsSchema] = []
    add_annotations_to_page(pdf_page, annotations, pdf_writer)

    # Verify page was not modified
    assert "/Annots" not in pdf_page or len(pdf_page["/Annots"]) == 0
    assert pdf_page.get_contents() is None


def test_coordinate_conversion(pdf_page, pdf_writer):
    """Test coordinate conversion from relative to absolute"""
    annotations = [
        UserAnnotationsSchema(
            annotation_type=AnnotationType.HIGHLIGHT,
            bbox_top=0.5,  # Middle of page
            bbox_left=0.5,  # Middle of page
            bbox_width=0.1,
            bbox_height=0.1,
            hexcolor="#FFFF00",
        )
    ]

    add_annotations_to_page(pdf_page, annotations, pdf_writer)

    highlight = pdf_page["/Annots"][0].get_object()
    rect = highlight["/Rect"]

    # Check if coordinates are properly converted to absolute values
    page_height = float(pdf_page.mediabox.height)
    page_width = float(pdf_page.mediabox.width)

    expected_left = 0.5 * page_width
    expected_width = 0.1 * page_width
    expected_top = page_height - (0.5 * page_height)
    expected_height = 0.1 * page_height

    # Convert PyPDF2 FloatObjects to Python floats for comparison
    rect_left = float(rect[0])
    rect_right = float(rect[2])
    rect_bottom = float(rect[1])
    rect_top = float(rect[3])

    assert abs(rect_left - expected_left) < 1  # Left
    assert abs(rect_right - (expected_left + expected_width)) < 1  # Right
    assert abs(rect_bottom - (expected_top - expected_height)) < 1  # Bottom
    assert abs(rect_top - expected_top) < 1  # Top


def test_check_text_overflow_empty_text():
    """Test that empty text never overflows"""
    styles = getSampleStyleSheet()
    style = styles["Normal"]
    assert not check_text_overflow("", 100, 100, style)
    assert not check_text_overflow(None, 100, 100, style)


def test_check_text_overflow_single_line():
    """Test overflow detection for single line text"""
    styles = getSampleStyleSheet()
    style = styles["Normal"]
    style.fontSize = 10  # Ensure consistent font size

    # Short text that should fit
    assert not check_text_overflow("Short text", 100, 20, style)

    # Text too wide for the frame
    assert check_text_overflow(
        "This is a very long text that should not fit in a narrow frame", 50, 20, style
    )

    # Text too tall for the frame (due to font size)
    assert check_text_overflow("Short text", 100, 5, style)


def test_check_text_overflow_multi_line():
    """Test overflow detection for text that wraps to multiple lines"""
    styles = getSampleStyleSheet()
    style = styles["Normal"]
    style.fontSize = 10

    text = (
        "This is a longer text that should wrap to multiple lines. "
        "It should fit in a wider frame but overflow in a narrow one."
    )

    # Should fit in a wider, taller frame
    assert not check_text_overflow(text, 200, 100, style)

    # Should overflow in a narrow frame (forces more line wrapping)
    assert check_text_overflow(text, 50, 100, style)

    # Should overflow in a short frame (not enough height for wrapped lines)
    assert check_text_overflow(text, 200, 20, style)


def test_check_text_overflow_with_different_styles():
    """Test overflow detection with different text styles"""
    styles = getSampleStyleSheet()
    base_style = styles["Normal"]

    text = "This is a test text for different styles"
    frame_width = 100
    frame_height = 30

    # Test with different font sizes
    small_style = ParagraphStyle("Small", parent=base_style, fontSize=8)
    large_style = ParagraphStyle("Large", parent=base_style, fontSize=16)

    # Should fit with small font
    assert not check_text_overflow(text, frame_width, frame_height, small_style)

    # Should overflow with large font
    assert check_text_overflow(text, frame_width, frame_height, large_style)


def test_check_text_overflow_extreme_cases():
    """Test overflow detection with extreme cases"""
    styles = getSampleStyleSheet()
    style = styles["Normal"]

    # Very long single word
    long_word = "supercalifragilisticexpialidocious" * 30
    assert check_text_overflow(long_word, 100, 100, style)

    # Zero or negative dimensions
    assert check_text_overflow("Some text", 0, 100, style)
    assert check_text_overflow("Some text", 100, 0, style)
    assert check_text_overflow("Some text", -10, -10, style)

    # Very large dimensions
    assert not check_text_overflow("Some text", 1000, 1000, style)


def test_find_optimal_font_size():
    """Test finding the optimal font size for text to fit in a box"""
    styles = getSampleStyleSheet()
    base_style = styles["Normal"]

    # Test with empty text - should return the base font size
    assert find_optimal_font_size("", 100, 100, base_style) == base_style.fontSize

    # Test with short text in a large box - should find a large font size
    short_text = "Short text"
    large_font_size = find_optimal_font_size(short_text, 200, 100, base_style)
    assert large_font_size > 10  # Should be larger than default

    # Test with long text in a small box - should find a small font size
    long_text = "This is a very long text that needs to fit in a small box. " * 5
    small_font_size = find_optimal_font_size(long_text, 100, 50, base_style)
    assert small_font_size < 10  # Should be smaller than default

    # Test with extremely long text in a tiny box - should find the minimum font size
    very_long_text = (
        "This is an extremely long text that cannot possibly fit in a tiny box without using a very small font size. "
        * 10
    )
    tiny_box_font_size = find_optimal_font_size(very_long_text, 50, 20, base_style)
    assert 2 <= tiny_box_font_size <= 7  # Should be close to the minimum (2)


def test_comment_font_size_adjustment(pdf_page, pdf_writer, caplog):
    """Test that comment text font size is automatically adjusted to fit the box"""
    import structlog

    structlog.get_logger()

    # Create a comment with text that will need font size adjustment
    long_text = (
        "This is a very long comment that will need font size adjustment to fit in the box. "
        * 5
    )
    small_height = 0.05  # Very small height that will need font size adjustment

    annotations = [
        UserAnnotationsSchema(
            annotation_type=AnnotationType.COMMENT,
            bbox_top=0.2,
            bbox_left=0.1,
            bbox_width=0.3,
            bbox_height=small_height,
            text=long_text,
        )
    ]

    # Add the annotation and check logs for font size adjustment
    with caplog.at_level("INFO"):
        add_annotations_to_page(pdf_page, annotations, pdf_writer)

    # Verify that font size adjustment was logged
    found_adjustment_log = False
    for record in caplog.records:
        if "Using optimal font size for comment box" in str(record.msg):
            found_adjustment_log = True
            break
    assert (
        found_adjustment_log
    ), "Expected to find log about using optimal font size for comment box"

    # Verify the content was added
    assert pdf_page.get_contents() is not None
