import pytest
from django.contrib.auth import get_user_model
from django.contrib.auth.models import AbstractUser
from django.db.models import QuerySet
from django.urls import reverse

from dossier.fakes import add_some_fake_semantic_documents
from dossier.models import <PERSON>ccount, Dossier, DocumentCategory
from dossier.services import create_dossier
from semantic_document.helpers import (
    get_semantic_documents_with_incorrect_page_numbering,
)
from semantic_document.models import SemanticDocument, SemanticPage
from semantic_document.schemas import (
    MoveSemanticPageSchema,
    MoveSemanticPagesToNewDocumentSchema,
    MoveSemanticPagesToNewDocumentResponseSchema,
)

User: AbstractUser = get_user_model()


@pytest.fixture
def test_dossier(db):
    Dossier.objects.all().delete()
    account = Account.objects.get(key="default")
    user = User.objects.get(username="<EMAIL>")
    dossier = create_dossier(
        account,
        "source_test_dossier",
        "de",
        user,
    )
    dossier.save()
    return dossier, account


def test_correctly_numbered_pages_move_into_new_dossier(test_dossier, testuser1_client):
    test_dossier, account = test_dossier

    source_semantic_document: SemanticDocument = add_some_fake_semantic_documents(
        test_dossier,
        num_docs=1,
        allow_empty_docs=False,
        max_pages=3,
        min_num_pages=3,
    )[0]

    # Verify initial state
    source_semantic_document_ordered_semantic_pages: QuerySet[SemanticPage] = (
        source_semantic_document.semantic_pages.all().order_by("number")
    )
    assert source_semantic_document_ordered_semantic_pages[0].number == 0
    assert source_semantic_document_ordered_semantic_pages[1].number == 1
    assert source_semantic_document_ordered_semantic_pages[2].number == 2

    assert source_semantic_document_ordered_semantic_pages.count() == 3

    # Use existing function to check correct ordering
    results = get_semantic_documents_with_incorrect_page_numbering(account)
    assert len(results["start_at_one"]) == 0
    assert len(results["start_at_two_or_more"]) == 0
    assert len(results["non_consecutive"]) == 0

    page_request = MoveSemanticPageSchema(
        uuid=str(source_semantic_document_ordered_semantic_pages[1].uuid),
        source_file_uuid=str(
            source_semantic_document_ordered_semantic_pages[
                1
            ].processed_page.processed_file.uuid
        ),
        number=1,
        page_objects=[],
    )

    document_category = DocumentCategory.objects.get(
        name="FINANCING_MISC", account=account
    )

    request = MoveSemanticPagesToNewDocumentSchema(
        semantic_pages=[page_request],
        document_category_id=document_category.id,
        document_category_name=document_category.name,
    )

    result = testuser1_client.post(
        reverse(
            "api:move-semantic-pages-to-new-document",
            kwargs={"dossier_uuid": test_dossier.uuid},
        ),
        data=request.model_dump_json(),
        content_type="application/json",
    )
    assert result.status_code == 200

    response = MoveSemanticPagesToNewDocumentResponseSchema.model_validate_json(
        result.content
    )

    new_semantic_document = SemanticDocument.objects.get(
        uuid=response.semantic_document_uuid
    )

    assert new_semantic_document.semantic_pages.count() == 1
    assert new_semantic_document.semantic_pages.first().number == 0

    source_semantic_document.refresh_from_db()

    source_semantic_document_ordered_semantic_pages = (
        source_semantic_document.semantic_pages.all().order_by("number")
    )

    assert source_semantic_document_ordered_semantic_pages.count() == 2

    assert source_semantic_document_ordered_semantic_pages[0].number == 0
    assert source_semantic_document_ordered_semantic_pages[1].number == 1
