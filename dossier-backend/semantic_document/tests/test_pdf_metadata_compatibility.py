"""
Tests to verify PDF metadata functions work with both schema objects and model instances.
This test specifically addresses the AttributeError and TypeError issues that were fixed.
"""

import pytest
from unittest.mock import Mock, patch


class TestPdfMetadataCompatibility:
    """Test that PDF metadata functions work with both SemanticDocumentFullApiData and SemanticDocument objects."""

    def test_attribute_access_patterns_no_errors(self):
        """Test that the specific attribute access patterns don't raise AttributeError."""
        # Test the exact pattern that was failing before the fix

        # Create objects with different attribute patterns using simple classes
        class SchemaObj:
            def __init__(self):
                self.suffix = "test_suffix"  # Has suffix, not title_suffix

        class ModelObj:
            def __init__(self):
                self.title_suffix = "test_suffix"  # Has title_suffix, not suffix

        class NoSuffixObj:
            pass  # Has neither

        schema_obj = SchemaObj()
        model_obj = ModelObj()
        no_suffix_obj = NoSuffixObj()

        # Test the fixed attribute access pattern
        def get_suffix_value(document):
            """The pattern used in the fixed code."""
            if hasattr(document, "suffix"):
                return document.suffix
            elif hasattr(document, "title_suffix"):
                return document.title_suffix
            return None

        # These should all work without AttributeError
        assert get_suffix_value(schema_obj) == "test_suffix"
        assert get_suffix_value(model_obj) == "test_suffix"
        assert get_suffix_value(no_suffix_obj) is None

    def test_semantic_pages_access_patterns_no_errors(self):
        """Test that semantic pages access patterns don't raise TypeError."""
        # Test the exact pattern that was failing before the fix

        # Create objects with different semantic_pages patterns
        schema_obj = Mock()
        schema_obj.semantic_pages = []  # List (schema object)

        model_obj = Mock()
        mock_related_manager = Mock()
        mock_related_manager.all = Mock(return_value=[])
        model_obj.semantic_pages = mock_related_manager  # RelatedManager (model object)

        # Test the fixed semantic pages access pattern
        def get_semantic_pages(document):
            """The pattern used in the fixed code."""
            if hasattr(document, 'semantic_pages'):
                if hasattr(document.semantic_pages, 'all'):
                    # Django RelatedManager - call .all() to get queryset
                    return document.semantic_pages.all()
                else:
                    # List from schema object
                    return document.semantic_pages
            return None

        # These should all work without TypeError
        assert get_semantic_pages(schema_obj) == []
        assert get_semantic_pages(model_obj) == []

        # Test with actual content
        mock_page = Mock()
        schema_obj.semantic_pages = [mock_page]
        model_obj.semantic_pages.all.return_value = [mock_page]

        assert len(get_semantic_pages(schema_obj)) == 1
        assert len(get_semantic_pages(model_obj)) == 1

    def test_original_error_scenarios_fixed(self):
        """Test that the original error scenarios are now fixed."""

        # Scenario 1: SemanticDocument model object trying to access 'suffix'
        # This was causing: AttributeError: 'SemanticDocument' object has no attribute 'suffix'
        model_doc = Mock()
        model_doc.title_suffix = "test_suffix"
        # Ensure it does NOT have a 'suffix' attribute
        if hasattr(model_doc, 'suffix'):
            delattr(model_doc, 'suffix')

        # This should not raise AttributeError
        try:
            if hasattr(model_doc, "suffix"):
                suffix = model_doc.suffix
            elif hasattr(model_doc, "title_suffix"):
                suffix = model_doc.title_suffix
            else:
                suffix = None
            assert suffix == "test_suffix"
        except AttributeError as e:
            pytest.fail(f"AttributeError should not be raised: {e}")

        # Scenario 2: RelatedManager semantic_pages trying to use len()
        # This was causing: TypeError: object of type 'RelatedManager' has no len()
        model_doc_with_pages = Mock()
        mock_related_manager = Mock()
        mock_related_manager.all = Mock(return_value=[])
        model_doc_with_pages.semantic_pages = mock_related_manager

        # This should not raise TypeError
        try:
            semantic_pages = None
            if hasattr(model_doc_with_pages, 'semantic_pages'):
                if hasattr(model_doc_with_pages.semantic_pages, 'all'):
                    # Django RelatedManager - call .all() to get queryset
                    semantic_pages = model_doc_with_pages.semantic_pages.all()
                else:
                    # List from schema object
                    semantic_pages = model_doc_with_pages.semantic_pages

            if semantic_pages and len(semantic_pages) > 0:
                # This would have failed before the fix
                pass
            assert semantic_pages == []
        except TypeError as e:
            pytest.fail(f"TypeError should not be raised: {e}")

    def test_functions_can_be_called_without_exceptions(self):
        """Test that the actual functions can be called without the original exceptions."""
        from semantic_document.services_pdf import create_pdf_semantic_document_metadata
        from semantic_document.pdf_metadata_enhanced import create_enhanced_pdf_metadata

        # Create minimal mock objects that won't trigger Pydantic validation
        # but will test the attribute access patterns

        # Test that the functions can handle the attribute access without errors
        # We'll catch any Pydantic validation errors but ensure no AttributeError/TypeError

        # Mock for model object
        model_obj = Mock()
        model_obj.title_suffix = "test"
        mock_related_manager = Mock()
        mock_related_manager.all = Mock(return_value=[])
        model_obj.semantic_pages = mock_related_manager

        # Test that the attribute access patterns work
        with patch('semantic_document.services_pdf.get_document_categories_by_name'):
            try:
                # This should not raise AttributeError or TypeError
                # (It may raise ValidationError due to Mock objects, but that's expected)
                create_pdf_semantic_document_metadata(model_obj)
            except AttributeError as e:
                if "'SemanticDocument' object has no attribute 'suffix'" in str(e):
                    pytest.fail("Original AttributeError was not fixed")
                # Re-raise if it's a different AttributeError
                raise
            except TypeError as e:
                if "object of type 'RelatedManager' has no len()" in str(e):
                    pytest.fail("Original TypeError was not fixed")
                # Re-raise if it's a different TypeError
                raise
            except Exception:
                # Other exceptions (like ValidationError) are expected with Mock objects
                pass

        with patch('semantic_document.pdf_metadata_enhanced.get_document_categories_by_name'):
            try:
                # This should not raise AttributeError or TypeError
                create_enhanced_pdf_metadata(model_obj)
            except AttributeError as e:
                if "'SemanticDocument' object has no attribute 'suffix'" in str(e):
                    pytest.fail("Original AttributeError was not fixed")
                raise
            except TypeError as e:
                if "object of type 'RelatedManager' has no len()" in str(e):
                    pytest.fail("Original TypeError was not fixed")
                raise
            except Exception:
                # Other exceptions are expected with Mock objects
                pass
