{"document_category_id": "212", "document_category_name": "PASSPORT_IT", "applicable_pages": [{"uuid": "993e3f85-5562-4869-82d0-f510173c818b", "source_file_uuid": "6418888c-b718-41c2-8f08-a547597c370f", "number": 0, "page_objects": [{"uuid": "c3ec1a98-0059-45c1-a2c7-10a979cd2282", "key": "firstname", "title": "<PERSON><PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON><PERSON>", "en": "Firstname", "fr": "[Firstname]", "it": "[Firstname]"}, "visible": true, "value": "<PERSON>»", "type": "STRING", "bbox": {"ref_width": 2379, "ref_height": 3990, "top": 3041, "left": 1080, "right": 1414, "bottom": 3129}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "993e3f85-5562-4869-82d0-f510173c818b"}, {"uuid": "07f9566f-bff0-40b3-9066-d46f9d9f37f2", "key": "identity_cover_misc", "title": "identity_cover_misc", "titles": {"de": "identity_cover_misc", "en": "identity_cover_misc", "fr": "identity_cover_misc", "it": "identity_cover_misc"}, "visible": true, "value": null, "type": "IMAGE", "bbox": {"ref_width": 661, "ref_height": 1109, "top": 384, "left": 146, "right": 649, "bottom": 745}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "993e3f85-5562-4869-82d0-f510173c818b"}, {"uuid": "a9058558-9d58-446a-a343-064cacce1bf0", "key": "identity_swiss_passport_first", "title": "identity_swiss_passport_first", "titles": {"de": "identity_swiss_passport_first", "en": "identity_swiss_passport_first", "fr": "identity_swiss_passport_first", "it": "identity_swiss_passport_first"}, "visible": true, "value": null, "type": "IMAGE", "bbox": {"ref_width": 661, "ref_height": 1109, "top": 744, "left": 151, "right": 637, "bottom": 1105}, "page_number": 0, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "993e3f85-5562-4869-82d0-f510173c818b"}], "document_category": {"id": "371", "name": "LEASING_AGREEMENT", "de": null, "en": null, "fr": null, "it": null, "de_external": null, "en_external": null, "fr_external": null, "it_external": null, "additional_search_terms_de": null, "additional_search_terms_en": null, "additional_search_terms_fr": null, "additional_search_terms_it": null}}, {"uuid": "95d02581-940b-4b93-8d09-8a089bd57b12", "source_file_uuid": "696ae4a5-2238-451a-aa60-9a6a451c2f1c", "number": 1, "page_objects": [{"uuid": "e55ebd1e-3354-43ec-8fbf-825530f71bba", "key": "p2_income_employed_main", "title": "P2 Einkommen Haupterwerb", "titles": {"de": "P2 Einkommen Haupterwerb", "en": "P2 Main Income", "fr": "P2 Revenu net dépendante", "it": "[P2 Main Income]"}, "visible": true, "value": "CHF 128'991", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 360, "left": 2640, "right": 2796, "bottom": 427}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "95d02581-940b-4b93-8d09-8a089bd57b12"}, {"uuid": "63890512-dce5-4072-bbe5-54a67036feb8", "key": "fh_landwirtschaft", "title": "Landwirtschaft", "titles": {"de": "Landwirtschaft", "en": "Landwirtschaft", "fr": "Landwirtschaft", "it": "Landwirtschaft"}, "visible": false, "value": "{\"value\": \"Landwirtschaft\", \"value_found\": \"Landwirtschaft\", \"classifier\": \"hypodossier-models/finhurdles_spacy/finhurdles_de_20220502_1529-20220502_1535.tar.gz\", \"context\": \"2.    Eink\\u00fcnfte aus selbst\\u00e4ndiger Erwerbst\\u00e4tigkeit in Handel, Gewerbe, \\nAutorenrechte usw.\\nfreien Berufen (Hilfsblatt A) oder Landwirtschaft (Hilfsblatt B oder G)\\n2: Inklusive Ertr\\u00e4ge aus                 2.1  Haupterwerb           Ehemann / Einzelperson / P1                     Hilfsblatt   120\"}", "type": "FINHURDLE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 719, "left": 1343, "right": 1605, "bottom": 775}, "page_number": 1, "confidence": 0.04614436998963356, "confidence_summary": {"value_formatted": "5%", "value": 0.04614436998963356, "level": "low"}, "semantic_page_uuid": "95d02581-940b-4b93-8d09-8a089bd57b12"}, {"uuid": "7ef6e61c-a037-4531-b58b-6aed24336d93", "key": "income_real_estate_net_primary", "title": "Primärliegenschaft Ertrag netto", "titles": {"de": "Primärliegenschaft Ertrag netto", "en": "Primary Self used Real Estate Income net", "fr": "[Primary Self used Real Estate Income net]", "it": "[Primary Self used Real Estate Income net]"}, "visible": true, "value": "CHF 20'560", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3588, "left": 2662, "right": 2802, "bottom": 3642}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "95d02581-940b-4b93-8d09-8a089bd57b12"}, {"uuid": "d18b2c4d-33aa-45e9-a7ba-8bc55a04c245", "key": "income_portfolio", "title": "Wertschriftenerträge", "titles": {"de": "Wertschriftenerträge", "en": "Income Portfolio", "fr": "Revenue mobilier", "it": "[Income Portfolio]"}, "visible": true, "value": "CHF 15", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2341, "left": 2753, "right": 2803, "bottom": 2408}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "95d02581-940b-4b93-8d09-8a089bd57b12"}, {"uuid": "5ddb98b4-41d9-43f1-906f-42eb991010e4", "key": "p2_income_child_benefits", "title": "P2 Einkünfte Kinder- und Familienzulagen", "titles": {"de": "P2 Einkünfte Kinder- und Familienzulagen", "en": "P2 Income Child Benefits", "fr": "[P2 Income Child Benefits]", "it": "[P2 Income Child Benefits]"}, "visible": true, "value": "CHF 4'800", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2190, "left": 2686, "right": 2802, "bottom": 2244}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "95d02581-940b-4b93-8d09-8a089bd57b12"}, {"uuid": "d95ffed1-f4c1-4889-89f4-c84e8045bdc0", "key": "fh_geschieden", "title": "Scheidung", "titles": {"de": "Scheidung", "en": "Scheidung", "fr": "Divorce", "it": "Divorce"}, "visible": true, "value": "{\"value\": \"geschieden\", \"value_found\": \"geschieden\", \"classifier\": \"hypodossier-models/finhurdles_spacy/finhurdles_de_20220502_1529-20220502_1535.tar.gz\", \"context\": \"5.   \\u00dcbrige Eink\\u00fcnfte und Gewinne\\nName/Adresse Alimentenzahler/in\\n5.1   Unterhaltsbeitr\\u00e4ge vom geschiedenen / getrennten Ehegatten / Partn.                  160\"}", "type": "FINHURDLE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2611, "left": 1194, "right": 1387, "bottom": 2665}, "page_number": 1, "confidence": 0.2610735595226288, "confidence_summary": {"value_formatted": "26%", "value": 0.2610735595226288, "level": "low"}, "semantic_page_uuid": "95d02581-940b-4b93-8d09-8a089bd57b12"}, {"uuid": "9bbbda43-492e-4ff0-aa16-14ad22518c3f", "key": "property_imputed_rental_value", "title": "Liegenschaft Eigenmietwert", "titles": {"de": "Liegenschaft Eigenmietwert", "en": "Imputed Rental Value", "fr": "Valeur locative", "it": "Valore locativo"}, "visible": true, "value": "CHF 25'700", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3111, "left": 1971, "right": 2110, "bottom": 3165}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "95d02581-940b-4b93-8d09-8a089bd57b12"}, {"uuid": "7f885fe2-f238-4ff3-810d-d1dbdbeb1059", "key": "income_real_estate_gross", "title": "Liegenschaftenertrag brutto", "titles": {"de": "Liegenschaftenertrag brutto", "en": "Income Real Estate gross", "fr": "Revenu brut immobilier", "it": "[Income Real Estate gross]"}, "visible": true, "value": "CHF 25'700", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3305, "left": 1971, "right": 2110, "bottom": 3358}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "95d02581-940b-4b93-8d09-8a089bd57b12"}, {"uuid": "0de8e719-0480-479d-8d61-92a57279f370", "key": "fh_alimente", "title": "Alimente", "titles": {"de": "Alimente", "en": "Alimente", "fr": "Alimente", "it": "Alimente"}, "visible": true, "value": "{\"value\": \"<PERSON><PERSON><PERSON>\", \"value_found\": \"Alimente\", \"classifier\": \"hypodossier-models/finhurdles_spacy/finhurdles_de_20220502_1529-20220502_1535.tar.gz\", \"context\": \"5.1 und 5.2\\n5.   \\u00dcbrige Eink\\u00fcnfte und Gewinne\\nName/Adresse Alimentenzahler/in\\n5.1   Unterhaltsbeitr\\u00e4ge vom geschiedenen / getrennten Ehegatten / Partn.                  160\"}", "type": "FINHURDLE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2566, "left": 366, "right": 497, "bottom": 2620}, "page_number": 1, "confidence": 0.16822287440299988, "confidence_summary": {"value_formatted": "17%", "value": 0.16822287440299988, "level": "low"}, "semantic_page_uuid": "95d02581-940b-4b93-8d09-8a089bd57b12"}, {"uuid": "635cd23c-1566-46dc-a916-9b4943833a7f", "key": "p1_income_employed_side", "title": "P1 Einkommen Nebenverdienst", "titles": {"de": "P1 Einkommen Nebenverdienst", "en": "P1 Additional Income", "fr": "[P1 Additional Income]", "it": "[P1 Additional Income]"}, "visible": true, "value": "CHF 7'502", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 459, "left": 2688, "right": 2802, "bottom": 514}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "95d02581-940b-4b93-8d09-8a089bd57b12"}, {"uuid": "fb1d780e-24aa-4f87-8c35-ff266fc37331", "key": "property_maintenance_cost", "title": "Liegenschaft Unterhaltskosten", "titles": {"de": "Liegenschaft Unterhaltskosten", "en": "Maintenance Cost", "fr": "Charges et frais d'entretien d'immeuble", "it": "[Maintenance Cost]"}, "visible": true, "value": "CHF 5'140", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3397, "left": 1996, "right": 2110, "bottom": 3451}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "95d02581-940b-4b93-8d09-8a089bd57b12"}, {"uuid": "e6cf3875-f643-45ce-ba5f-f9452fd8de7d", "key": "income_gross_total", "title": "Total der Einkünfte (brutto)", "titles": {"de": "Total der Einkünfte (brutto)", "en": "Total Income gross", "fr": "[Total Income gross]", "it": "[Total Income gross]"}, "visible": true, "value": "CHF 188'762", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3765, "left": 2640, "right": 2802, "bottom": 3833}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "95d02581-940b-4b93-8d09-8a089bd57b12"}, {"uuid": "ee35de32-2f59-4f9d-b046-db06bb5aee29", "key": "fh_arbeitslos", "title": "arbeitslos", "titles": {"de": "arbeitslos", "en": "arbeitslos", "fr": "arbeitslos", "it": "arbeitslos"}, "visible": true, "value": "{\"value\": \"arbeitslos\", \"value_found\": \"Arbeitslos\", \"classifier\": \"hypodossier-models/finhurdles_spacy/finhurdles_de_20220502_1529-20220502_1535.tar.gz\", \"context\": \"3.3  Erwerbsausfallentsch\\u00e4digungen aus Arbeitslosenversicherung\\n3.3: Direkt ausbezahlte Erwerbs-\\nausfallentsch\\u00e4digungen.                   Ehemann / Einzelperson / P1                                    Bescheinigung 140\"}", "type": "FINHURDLE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1801, "left": 1441, "right": 1624, "bottom": 1857}, "page_number": 1, "confidence": 0.21327336132526398, "confidence_summary": {"value_formatted": "21%", "value": 0.21327336132526398, "level": "low"}, "semantic_page_uuid": "95d02581-940b-4b93-8d09-8a089bd57b12"}, {"uuid": "e1569f2a-04aa-4eef-9f58-aa42728c36cb", "key": "document_date", "title": "Datum", "titles": {"de": "Datum", "en": "Date", "fr": "[Date]", "it": "[Date]"}, "visible": true, "value": "20.04.2020", "type": "DATE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3910, "left": 269, "right": 2834, "bottom": 4124}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "95d02581-940b-4b93-8d09-8a089bd57b12"}, {"uuid": "05d008e8-9d82-4c03-8bec-ff45aebf447f", "key": "p1_ahv_new", "title": "P1 Neue AHV-Nr.", "titles": {"de": "P1 Neue AHV-Nr.", "en": "P1 New AHV No.", "fr": "[P1 New AHV No.]", "it": "[P1 New AHV No.]"}, "visible": true, "value": "756.4078.6666.31", "type": "STRING", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 3910, "left": 269, "right": 2834, "bottom": 4124}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "95d02581-940b-4b93-8d09-8a089bd57b12"}, {"uuid": "fdddaa16-fcc1-401e-b4f2-a2357e073558", "key": "fh_gewerbe", "title": "Gewerbe", "titles": {"de": "Gewerbe", "en": "Gewerbe", "fr": "Gewerbe", "it": "Gewerbe"}, "visible": false, "value": "{\"value\": \"<PERSON><PERSON><PERSON><PERSON>\", \"value_found\": \"<PERSON><PERSON>erbe\", \"classifier\": \"hypodossier-models/finhurdles_spacy/finhurdles_de_20220502_1529-20220502_1535.tar.gz\", \"context\": \"Verwaltungsrats- und Vorstands-\\n<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, \\n2.    Eink\\u00fcnfte aus selbst\\u00e4ndiger Erwerbst\\u00e4tigkeit in Handel, Gewerbe, \\nAutorenrechte usw.\\nfreien Berufen (Hilfsblatt A) oder Landwirtschaft (Hilfsblatt B oder G)\"}", "type": "FINHURDLE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 652, "left": 1731, "right": 1888, "bottom": 708}, "page_number": 1, "confidence": 0.07572177052497864, "confidence_summary": {"value_formatted": "8%", "value": 0.07572177052497864, "level": "low"}, "semantic_page_uuid": "95d02581-940b-4b93-8d09-8a089bd57b12"}, {"uuid": "17c44624-2bec-455a-8d98-2aa121b31d63", "key": "fh_getrennt", "title": "<PERSON><PERSON><PERSON><PERSON>", "titles": {"de": "<PERSON><PERSON><PERSON><PERSON>", "en": "<PERSON><PERSON><PERSON><PERSON>", "fr": "<PERSON><PERSON><PERSON><PERSON>", "it": "<PERSON><PERSON><PERSON><PERSON>"}, "visible": true, "value": "{\"value\": \"getrennt\", \"value_found\": \"getrennt\", \"classifier\": \"hypodossier-models/finhurdles_spacy/finhurdles_de_20220502_1529-20220502_1535.tar.gz\", \"context\": \"5.   \\u00dcbrige Eink\\u00fcnfte und Gewinne\\nName/Adresse Alimentenzahler/in\\n5.1   Unterhaltsbeitr\\u00e4ge vom geschiedenen / getrennten Ehegatten / Partn.                  160\"}", "type": "FINHURDLE", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 2611, "left": 1449, "right": 1593, "bottom": 2665}, "page_number": 1, "confidence": 0.2610735595226288, "confidence_summary": {"value_formatted": "26%", "value": 0.2610735595226288, "level": "low"}, "semantic_page_uuid": "95d02581-940b-4b93-8d09-8a089bd57b12"}, {"uuid": "2f642a4f-2c86-4b07-8bfc-80d7ee9cabc3", "key": "p1_income_employed_main", "title": "P1 Einkommen Haupterwerb", "titles": {"de": "P1 Einkommen Haupterwerb", "en": "P1 Main Income", "fr": "P1 Revenu net dépendante", "it": "[P1 Main Income]"}, "visible": true, "value": "CHF 22'506", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 280, "left": 2662, "right": 2802, "bottom": 336}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "95d02581-940b-4b93-8d09-8a089bd57b12"}, {"uuid": "3a5d2ebc-fd0a-4208-9bde-4640c0784d07", "key": "p2_income_eo", "title": "P2 Einkünfte Erwerbsausfallentschädigung", "titles": {"de": "P2 Einkünfte Erwerbsausfallentschädigung", "en": "P2 Income EO", "fr": "[P2 Income EO]", "it": "[P2 Income EO]"}, "visible": true, "value": "CHF 4'388", "type": "CURRENCY", "bbox": {"ref_width": 2974, "ref_height": 4209, "top": 1939, "left": 2686, "right": 2802, "bottom": 2006}, "page_number": 1, "confidence": 0.95, "confidence_summary": {"value_formatted": "95%", "value": 0.95, "level": "high"}, "semantic_page_uuid": "95d02581-940b-4b93-8d09-8a089bd57b12"}], "document_category": {"id": "650", "name": "BUILDING_RIGHTS_AGREEMENT", "de": null, "en": null, "fr": null, "it": null, "de_external": null, "en_external": null, "fr_external": null, "it_external": null, "additional_search_terms_de": null, "additional_search_terms_en": null, "additional_search_terms_fr": null, "additional_search_terms_it": null}}]}