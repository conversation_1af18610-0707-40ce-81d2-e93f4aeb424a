"""
Enhanced PDF metadata handling with prefixed keywords and XMP support.
Uses Pydantic 2 for clean data modeling and validation.
"""

import xml.etree.ElementTree as ET
from typing import Optional, List, Dict, Any

import PyPDF2
import structlog
from PyPDF2 import PdfWriter
from PyPDF2.generic import (
    NameObject,
)
from pydantic import BaseModel, Field, ConfigDict, field_validator

from dossier.schemas import SemanticDocumentFullApiData
from dossier.doc_cat_helpers import get_document_categories_by_name

logger = structlog.get_logger()

HYPODOSSIER_METADATA_SCHEMA_VERSION = "2.0.0"


class PrefixedKeyword(BaseModel):
    """Represents a prefixed keyword with namespace and value."""

    model_config = ConfigDict(frozen=True)

    prefix: str = Field(
        ..., description="Namespace prefix (e.g., 'category', 'suffix')"
    )
    value: str = Field(..., description="The actual value")

    def __str__(self) -> str:
        return f"{self.prefix}:{self.value}"

    @classmethod
    def from_string(cls, keyword_str: str) -> Optional["PrefixedKeyword"]:
        """Parse a prefixed keyword from string format."""
        if ":" not in keyword_str:
            return None
        prefix, value = keyword_str.split(":", 1)
        return cls(prefix=prefix.strip(), value=value.strip())


class ParsedKeywords(BaseModel):
    """Result of parsing keywords with structured and searchable terms separated."""

    model_config = ConfigDict(frozen=True)

    structured: Dict[str, str] = Field(
        default_factory=dict, description="Structured metadata by prefix"
    )
    searchable: List[str] = Field(
        default_factory=list, description="Human-readable search terms"
    )

    @classmethod
    def from_keywords_string(cls, keywords_string: str) -> "ParsedKeywords":
        """Parse keywords string into structured and searchable components."""
        if not keywords_string:
            return cls()

        keywords = [k.strip() for k in keywords_string.split(",") if k.strip()]
        structured = {}
        searchable = []

        for keyword in keywords:
            prefixed = PrefixedKeyword.from_string(keyword)
            if prefixed:
                structured[prefixed.prefix] = prefixed.value
            else:
                searchable.append(keyword)

        return cls(structured=structured, searchable=searchable)


class XMPMetadata(BaseModel):
    """XMP metadata structure for PDF documents."""

    model_config = ConfigDict(frozen=True)

    # Dublin Core namespace
    dc_title: Optional[str] = Field(None, alias="dc:title")
    dc_creator: Optional[str] = Field(None, alias="dc:creator")
    dc_subject: List[str] = Field(default_factory=list, alias="dc:subject")
    dc_description: Optional[str] = Field(None, alias="dc:description")

    # PDF namespace
    pdf_keywords: Optional[str] = Field(None, alias="pdf:Keywords")
    pdf_producer: Optional[str] = Field(None, alias="pdf:Producer")

    # Custom HypoDossier namespace
    hypodossier_document_category_key: Optional[str] = Field(
        None, alias="hypodossier:DocumentCategoryKey"
    )
    hypodossier_title_suffix: Optional[str] = Field(
        None, alias="hypodossier:TitleSuffix"
    )
    hypodossier_version: Optional[str] = Field(None, alias="hypodossier:Version")
    hypodossier_uuid: Optional[str] = Field(None, alias="hypodossier:UUID")

    def to_xmp_xml(self) -> str:
        """Generate XMP XML string."""
        # XMP packet wrapper
        xmp_begin = '<?xpacket begin="﻿" id="W5M0MpCehiHzreSzNTczkc9d"?>'
        xmp_end = '<?xpacket end="w"?>'

        # Create root element with namespaces
        root = ET.Element("{adobe:ns:meta/}xmpmeta")
        root.set(
            "{http://www.w3.org/2001/XMLSchema-instance}schemaLocation",
            "adobe:ns:meta/ http://ns.adobe.com/xap/1.0/mm/",
        )

        # RDF wrapper
        rdf = ET.SubElement(root, "{http://www.w3.org/1999/02/22-rdf-syntax-ns#}RDF")
        description = ET.SubElement(
            rdf, "{http://www.w3.org/1999/02/22-rdf-syntax-ns#}Description"
        )
        description.set("{http://www.w3.org/1999/02/22-rdf-syntax-ns#}about", "")

        # Add namespace declarations
        description.set("xmlns:dc", "http://purl.org/dc/elements/1.1/")
        description.set("xmlns:pdf", "http://ns.adobe.com/pdf/1.3/")
        description.set("xmlns:hypodossier", "http://hypodossier.com/ns/1.0/")

        # Add metadata fields - use model_dump to get actual values including aliased fields
        data = self.model_dump(by_alias=True)

        # Dublin Core fields
        if data.get("dc:title"):
            dc_title = ET.SubElement(description, "dc:title")
            dc_title.text = data["dc:title"]

        if data.get("dc:creator"):
            dc_creator = ET.SubElement(description, "dc:creator")
            dc_creator.text = data["dc:creator"]

        if data.get("dc:description"):
            dc_description = ET.SubElement(description, "dc:description")
            dc_description.text = data["dc:description"]

        if data.get("dc:subject"):
            dc_subject = ET.SubElement(description, "dc:subject")
            bag = ET.SubElement(
                dc_subject, "{http://www.w3.org/1999/02/22-rdf-syntax-ns#}Bag"
            )
            for subject in data["dc:subject"]:
                li = ET.SubElement(
                    bag, "{http://www.w3.org/1999/02/22-rdf-syntax-ns#}li"
                )
                li.text = subject

        # PDF fields
        if data.get("pdf:Keywords"):
            pdf_keywords = ET.SubElement(description, "pdf:Keywords")
            pdf_keywords.text = data["pdf:Keywords"]

        if data.get("pdf:Producer"):
            pdf_producer = ET.SubElement(description, "pdf:Producer")
            pdf_producer.text = data["pdf:Producer"]

        # HypoDossier custom fields
        if data.get("hypodossier:DocumentCategoryKey"):
            hd_cat = ET.SubElement(description, "hypodossier:DocumentCategoryKey")
            hd_cat.text = data["hypodossier:DocumentCategoryKey"]

        if data.get("hypodossier:TitleSuffix"):
            hd_suffix = ET.SubElement(description, "hypodossier:TitleSuffix")
            hd_suffix.text = data["hypodossier:TitleSuffix"]

        if data.get("hypodossier:Version"):
            hd_version = ET.SubElement(description, "hypodossier:Version")
            hd_version.text = data["hypodossier:Version"]

        if data.get("hypodossier:UUID"):
            hd_uuid = ET.SubElement(description, "hypodossier:UUID")
            hd_uuid.text = data["hypodossier:UUID"]

        # Convert to string
        xml_str = ET.tostring(root, encoding="unicode", method="xml")
        return f"{xmp_begin}\n{xml_str}\n{xmp_end}"


class EnhancedPdfMetadata(BaseModel):
    """Enhanced PDF metadata model with Pydantic 2."""

    model_config = ConfigDict(frozen=True)

    version: str = Field(default=HYPODOSSIER_METADATA_SCHEMA_VERSION)
    uuid: str = Field(..., description="Document UUID")
    title: str = Field(..., description="Document title")
    document_category_key: str = Field(..., description="Document category key")
    document_category_name: str = Field(..., description="Human-readable category name")
    document_category_title_de: str = Field(..., description="German category title")
    document_category_title_en: str = Field(..., description="English category title")
    document_category_title_fr: str = Field(..., description="French category title")
    document_category_title_it: str = Field(..., description="Italian category title")
    title_suffix: Optional[str] = Field(None, description="Optional title suffix")

    @field_validator("uuid")
    @classmethod
    def validate_uuid(cls, v: str) -> str:
        """Ensure UUID is a string."""
        return str(v)

    def get_full_title(self) -> str:
        """Get full title with suffix if available."""
        if self.title_suffix:
            return f"{self.title} {self.title_suffix}"
        return self.title

    def create_prefixed_keywords(self) -> List[str]:
        """Create prefixed keywords for Option A implementation."""
        keywords = [
            f"category:{self.document_category_key}",
            f"type:{self.document_category_name}",
        ]

        if self.title_suffix:
            keywords.append(f"suffix:{self.title_suffix}")

        return keywords

    def create_searchable_keywords(self) -> List[str]:
        """Create human-readable search terms."""
        searchable = [self.document_category_name, "document", "official"]

        # Add suffix if it's human-readable (not just technical codes)
        if self.title_suffix and not self.title_suffix.isupper():
            searchable.append(self.title_suffix)

        return searchable

    def create_combined_keywords(self) -> str:
        """Create combined keywords string with prefixed and searchable terms."""
        prefixed = self.create_prefixed_keywords()
        searchable = self.create_searchable_keywords()
        all_keywords = prefixed + searchable
        return ", ".join(all_keywords)

    def to_xmp_metadata(self) -> XMPMetadata:
        """Convert to XMP metadata structure."""
        # Use the aliased field names when creating XMPMetadata
        return XMPMetadata(
            **{
                "dc:title": self.title,  # Use the localized title directly
                "dc:creator": "HypoDossier AG",
                "dc:subject": self.create_searchable_keywords(),
                "dc:description": f"Document category: {self.document_category_name}",
                "pdf:Keywords": self.create_combined_keywords(),
                "pdf:Producer": "Dossier Manager 2.0",
                "hypodossier:DocumentCategoryKey": self.document_category_key,
                "hypodossier:TitleSuffix": self.title_suffix,
                "hypodossier:Version": self.version,
                "hypodossier:UUID": self.uuid,
            }
        )


def create_enhanced_pdf_metadata(
    document: SemanticDocumentFullApiData,
    document_categories: Optional[Dict[str, Any]] = None,
) -> EnhancedPdfMetadata:
    """Create enhanced PDF metadata from semantic document."""
    if document_categories is None:
        document_categories = get_document_categories_by_name()
    doc_cat_name = document.document_category.name
    doc_cat = document_categories[doc_cat_name]

    # Extract dossier language from semantic pages if available
    dossier_lang = "de"  # Default fallback
    if document.semantic_pages and len(document.semantic_pages) > 0:
        dossier_lang = document.semantic_pages[0].lang.lower()

    # Create localized title using the dossier language
    # This follows the same logic as SemanticDocument.calculate_title
    if document.title_custom:
        localized_title = document.title_custom
    else:
        localized_category_name = doc_cat.translated(dossier_lang)
        localized_title = f"{document.document_category.id} {localized_category_name}"
        if document.suffix:
            localized_title += f" {document.suffix}"

    return EnhancedPdfMetadata(
        uuid=str(document.uuid),
        title=localized_title,
        document_category_key=document.document_category.name,
        document_category_name=doc_cat.translated(
            dossier_lang
        ),  # Use localized category name
        document_category_title_de=doc_cat.translated("de"),
        document_category_title_en=doc_cat.translated("en"),
        document_category_title_fr=doc_cat.translated("fr"),
        document_category_title_it=doc_cat.translated("it"),
        title_suffix=document.suffix,
    )


def create_document_info_properties_with_prefixed_keywords(
    metadata: EnhancedPdfMetadata,
) -> Dict[str, Any]:
    """
    Create PDF document info properties with prefixed keywords.
    Uses standard /Keywords field with namespace prefixes for reliable parsing.
    """
    return {
        "/Title": metadata.title,  # Use the localized title directly (already includes suffix if present)
        "/Author": "",
        "/Subject": metadata.title,  # Use the same localized title
        "/Keywords": metadata.create_combined_keywords(),
        "/Creator": "HypoDossier AG",
        "/Producer": "Dossier Manager 2.0",
    }


def parse_document_info_properties_with_prefixed_keywords(
    pdf_info: Dict[str, Any]
) -> Dict[str, Any]:
    """
    Parse PDF document info properties from Option A format.
    Reliably extracts structured data from prefixed keywords.
    """
    keywords_str = pdf_info.get("/Keywords", "")
    parsed_keywords = ParsedKeywords.from_keywords_string(keywords_str)

    return {
        "title": pdf_info.get("/Title", ""),
        "author": pdf_info.get("/Author", ""),
        "subject": pdf_info.get("/Subject", ""),
        "creator": pdf_info.get("/Creator", ""),
        "producer": pdf_info.get("/Producer", ""),
        "structured_metadata": parsed_keywords.structured,
        "searchable_keywords": parsed_keywords.searchable,
        "document_category_key": parsed_keywords.structured.get("category"),
        "document_type": parsed_keywords.structured.get("type"),
        "title_suffix": parsed_keywords.structured.get("suffix"),
    }


def add_xmp_metadata_to_pdf(writer: PdfWriter, metadata: EnhancedPdfMetadata) -> None:
    """
    Add XMP metadata to PDF writer.
    Creates structured XMP metadata with custom namespace support.
    """
    xmp_metadata = metadata.to_xmp_metadata()
    xmp_xml = xmp_metadata.to_xmp_xml()

    # Create XMP metadata stream object
    from PyPDF2.generic import StreamObject

    xmp_data = xmp_xml.encode("utf-8")

    # Create a stream object with the XMP data
    from PyPDF2.generic import NumberObject

    xmp_stream = StreamObject()
    xmp_stream.update(
        {
            NameObject("/Type"): NameObject("/Metadata"),
            NameObject("/Subtype"): NameObject("/XML"),
            NameObject("/Length"): NumberObject(len(xmp_data)),
        }
    )
    xmp_stream._data = xmp_data

    # Add to PDF catalog
    if "/Metadata" not in writer._root_object:
        metadata_ref = writer._add_object(xmp_stream)
        writer._root_object.update({NameObject("/Metadata"): metadata_ref})


def read_xmp_metadata_from_pdf(
    pdf_reader: PyPDF2.PdfReader,
) -> Optional[Dict[str, Any]]:
    """
    Read XMP metadata from PDF reader.
    Parses XMP XML and extracts structured metadata.
    """
    try:
        if "/Metadata" not in pdf_reader.metadata:
            return None

        metadata_obj = pdf_reader.metadata["/Metadata"]
        if hasattr(metadata_obj, "get_data"):
            xmp_data = metadata_obj.get_data()
        else:
            return None

        # Parse XMP XML
        xmp_str = xmp_data.decode("utf-8")

        # Remove XMP packet wrapper
        start_marker = "<x:xmpmeta"
        end_marker = "</x:xmpmeta>"
        start_idx = xmp_str.find(start_marker)
        end_idx = xmp_str.find(end_marker)

        if start_idx == -1 or end_idx == -1:
            return None

        xml_content = xmp_str[start_idx : end_idx + len(end_marker)]
        root = ET.fromstring(xml_content)

        # Extract metadata from XMP
        namespaces = {
            "x": "adobe:ns:meta/",
            "rdf": "http://www.w3.org/1999/02/22-rdf-syntax-ns#",
            "dc": "http://purl.org/dc/elements/1.1/",
            "pdf": "http://ns.adobe.com/pdf/1.3/",
            "hypodossier": "http://hypodossier.com/ns/1.0/",
        }

        description = root.find(".//rdf:Description", namespaces)
        if description is None:
            return None

        metadata = {}

        # Extract standard fields
        for field in ["title", "creator", "description"]:
            elem = description.find(f"dc:{field}", namespaces)
            if elem is not None:
                metadata[f"dc_{field}"] = elem.text

        # Extract subject array
        subject_elem = description.find("dc:subject", namespaces)
        if subject_elem is not None:
            bag = subject_elem.find("rdf:Bag", namespaces)
            if bag is not None:
                subjects = [li.text for li in bag.findall("rdf:li", namespaces)]
                metadata["dc_subject"] = subjects

        # Extract PDF fields
        for field in ["Keywords", "Producer"]:
            elem = description.find(f"pdf:{field}", namespaces)
            if elem is not None:
                metadata[f"pdf_{field.lower()}"] = elem.text

        # Extract HypoDossier custom fields
        for field in ["DocumentCategoryKey", "TitleSuffix", "Version", "UUID"]:
            elem = description.find(f"hypodossier:{field}", namespaces)
            if elem is not None:
                metadata[f"hypodossier_{field.lower()}"] = elem.text

        return metadata

    except Exception as e:
        logger.warning("Failed to parse XMP metadata", error=str(e))
        return None


# Combined implementation functions
def add_enhanced_metadata_to_pdf(
    writer: PdfWriter,
    document: SemanticDocumentFullApiData,
    document_categories: Optional[Dict[str, Any]] = None,
) -> None:
    """
    Add enhanced metadata to PDF using both Option A and XMP.

    Args:
        writer: PDF writer instance
        document: Semantic document data
        document_categories: Optional pre-fetched document categories to avoid repeated queries
    """
    metadata = create_enhanced_pdf_metadata(document, document_categories)

    # Add metadata (prefixed keywords in standard fields)
    doc_info = create_document_info_properties_with_prefixed_keywords(metadata)
    writer.add_metadata(doc_info)

    # Add XMP metadata for structured data
    add_xmp_metadata_to_pdf(writer, metadata)


def read_enhanced_metadata_from_pdf(pdf_reader: PyPDF2.PdfReader) -> Dict[str, Any]:
    """
    Read enhanced metadata from PDF using both Option A and XMP.

    Returns:
        Dictionary containing all available metadata from both sources
    """
    result = {
        "option_a_metadata": None,
        "xmp_metadata": None,
        "standard_metadata": None,
    }

    # Read standard PDF info
    if pdf_reader.metadata:
        standard_info = {k: v for k, v in pdf_reader.metadata.items()}
        result["standard_metadata"] = standard_info

        # Parse Option A if keywords are present
        if "/Keywords" in standard_info:
            result["option_a_metadata"] = (
                parse_document_info_properties_with_prefixed_keywords(standard_info)
            )

    # Read XMP metadata
    xmp_data = read_xmp_metadata_from_pdf(pdf_reader)
    if xmp_data:
        result["xmp_metadata"] = xmp_data

    return result
