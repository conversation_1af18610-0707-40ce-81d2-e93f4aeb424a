from typing import List, Optional
from uuid import UUID

from pydantic import BaseModel


class Status(BaseModel):
    uuid: UUID
    key: str
    name_de: Optional[str] = None
    name_en: Optional[str] = None
    name_fr: Optional[str] = None
    name_it: Optional[str] = None
    description_de: Optional[str] = None
    description_en: Optional[str] = None
    description_fr: Optional[str] = None
    description_it: Optional[str] = None

    color: str
    order: int


class Condition(BaseModel):
    uuid: UUID
    name: str
    show_anyway: bool
    overridable: bool

    warning_de: Optional[str] = None
    warning_title_de: Optional[str] = None
    warning_en: Optional[str] = None
    warning_title_en: Optional[str] = None
    warning_fr: Optional[str] = None
    warning_title_fr: Optional[str] = None
    warning_it: Optional[str] = None
    warning_title_it: Optional[str] = None

    fulfilled: Optional[bool] = None


class PossibleTransition(BaseModel):
    uuid: UUID
    from_state: Status
    to_state: Status
    checked_conditions: List[Condition]


class PossibleTransitions(BaseModel):
    transitions: List[PossibleTransition]
